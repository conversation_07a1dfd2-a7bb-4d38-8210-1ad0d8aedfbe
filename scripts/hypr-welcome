#!/bin/bash

# Define paths and scripts (adjust paths as per your setup)
logo_path="$HOME/.config/hypr/imgs/hypr-welcome.png"
hypr_information_script="$HOME/.config/hypr/scripts/yad_settings.sh"
hypr_keyhint_script="$HOME/.config/hypr/scripts/hypr-keyhint.sh"
hypr_swww_script="$HOME/.config/hypr/scripts/yad_swww.sh"
keyring_repair_script="$HOME/.config/hypr/scripts/keyring_repair.sh"
refresh_yay_script="$HOME/.config/hypr/scripts/refresh-yay.sh"
pgp_key_repair_script="$HOME/.config/hypr/scripts/pgp_key_repair.sh"
system_update_script="$HOME/.config/hypr/scripts/system-update.sh"
update_mirrors_script="$HOME/.config/hypr/scripts/reflector_simple.sh"
eos_rankmirrors_script="$HOME/.config/hypr/scripts/eos-rankmirrors.sh"
eos_log_tool_script="$HOME/.config/hypr/scripts/eos-log-tool.sh"
cleanup_script="$HOME/.config/hypr/scripts/cleanup.sh"
kitty_script="$HOME/.config/hypr/scripts/yad_update_dots.sh"
waybar_script="$HOME/.config/hypr/scripts/yad_switch-waybar-config.sh"
kill_script="$HOME/.config/hypr/scripts/kill-hypr-welcome"
hypr_blizz_update_script="$HOME/.config/hypr/scripts/hypr_blizz_update.sh"
hypr_welcome_update_script="$HOME/.config/hypr/scripts/hypr_welcome_update.sh"
hypr_waybar_update_script="$HOME/.config/hypr/scripts/hypr_waybar_update.sh"
hypr_blizz_repo="$HOME/hyprland-dots/Hyprland-blizz"
hypr_welcome_repo="$HOME/hyprland-dots/hypr-welcome"
hypr_waybar_repo="$HOME/hyprland-dots/hypr-waybar"

# Function to check network connectivity
check_network() {
    wget -q --spider http://google.com
    if [ $? -eq 0 ]; then
        return 0
    else
        echo "Network connection required. Please check your internet connection." >&2
        return 1
    fi
}

# Function to check for updates in a specific repository
check_updates() {
    local repo_dir="$1"
    cd "$repo_dir" || { echo "Failed to change to directory: $repo_dir." >&2; return 1; }
    git fetch origin main
    local commits_behind=$(git rev-list --count HEAD..origin/main)
    if [ "$commits_behind" -gt 0 ]; then
        return 0
    else
        return 1
    fi
}

# Check for updates in each repository
update_hypr_welcome=false
update_hypr_waybar=false
update_hypr_blizz=false

if check_updates "$hypr_welcome_repo"; then
    update_hypr_welcome=true
fi

if check_updates "$hypr_waybar_repo"; then
    update_hypr_waybar=true
fi

if check_updates "$hypr_blizz_repo"; then
    update_hypr_blizz=true
fi

# Determine the update status message
if $update_hypr_blizz || $update_hypr_welcome || $update_hypr_waybar; then
    update_status="<span foreground='#FF7F7F'>  Hyprland updates</span>"
else
    update_status="  No hyprland updates"
fi

# Echo to check the update status (for debugging)
echo "Update Status: $update_status"
if $update_hypr_blizz; then
    update_script="$hypr_blizz_update_script"
elif $update_hypr_welcome; then
    update_script="$hypr_welcome_update_script"
elif $update_hypr_waybar; then
    update_script="$hypr_waybar_update_script"
else
    update_script=""
fi
echo "Update Script: $update_script"


# Function to create the Yad dialog
create_yad_dialog() {
    yad --title="Welcome" \
        --image="$logo_path" \
        --image-on-top-center \
        --borders=3 \
        --geometry=900x410+800+200 \
        --tabs-position=top \
        --fixed \
        --no-buttons \
        --tab="System information" \
            --form \
            --columns=3 \
            --field="<b><span font_desc='CustomFont 10'></span>   General info</b>:FBTN" "bash -c 'yad --notebook --tab=\"Hardware and Drivers\" --form \
                --geometry=900x410+100+100 \
                --columns=3 \
                --image=\"$logo_path\" \
                --image-on-top-center \
                --field=\"󰖟   EndeavourOS:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://endeavouros.com/\" \
                --field=\"󰖟   Wiki:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://discovery.endeavouros.com/\" \
                --field=\"󰖟   News:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://endeavouros.com/news/\" \
                --field=\"󰖟   Forum:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://forum.endeavouros.com/\" \
                --field=\"󰖟   Donate:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://endeavouros.com/donate/\" \
                --field=\"󰖟   About hypr-welcome:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://github.com/RedBlizard/hypr-welcome/\" \
                --button=\"   Back to main menu\"'" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span font_desc='CustomFont 10'></span> Wiki Hyprland:FBTN" "bash $HOME/.config/hypr/scripts/welcome_browser.sh https://wiki.hyprland.org/" \
            --field="Waybar Wiki:FBTN" "bash $HOME/.config/hypr/scripts/welcome_browser.sh https://github.com/Alexays/Waybar/wiki" \
            --field="Discovery:FBTN" "bash $HOME/.config/hypr/scripts/welcome_browser.sh https://discovery.endeavouros.com/" \
            --field="Arch Wiki:FBTN" "bash $HOME/.config/hypr/scripts/welcome_browser.sh https://wiki.archlinux.org/" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span font_desc='CustomFont 10'> </span> PGP Key Repair:FBTN" "bash $pgp_key_repair_script" \
        --tab="Assistant" \
            --form \
            --columns=3 \
            --field="<b><span font_desc='CustomFont 10'>󱌢</span>   Assistant</b>:FBTN" "bash -c 'yad --notebook --tab=\"Assistant\" --form \
                --geometry=900x410+100+100 \
                --columns=3 \
                --image=\"$logo_path\" \
                --image-on-top-center \
                --field=\"   System update:FBTN\" \"bash $system_update_script\" \
                --field=\"󱍸   Update Mirrors:FBTN\" \"bash $update_mirrors_script\" \
                --field=\"󱍸   Rank Mirrors:FBTN\" \"bash $eos_rankmirrors_script\" \
                --field=\"   System Logs:FBTN\" \"bash $eos_log_tool_script\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"󰖟    All Arch packages:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://archlinux.org/packages/\" \
                --field=\"󰖟    All Aur packages:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://aur.archlinux.org/packages/\" \
                --field=\"󰖟    All applications:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://wiki.archlinux.org/title/List_of_applications/\" \
                --field=\"󰖟   Visit Hyprland:FBTN\" \"bash $HOME/.config/hypr/scripts/welcome_browser.sh https://hyprland.org/\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"󰤌   Edit Hyprland / Waybar :FBTN\" \"bash $hypr_information_script\" \
                --field=\"   Keyhint :FBTN\" \"bash $hypr_keyhint_script\" \
                --field=\"   Swww settings :FBTN\" \"bash $hypr_swww_script\" \
                --field=\"󱌢   Maintenance :FBTN\" \"bash $cleanup_script\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --field=\"<span foreground='white'> </span>                                        :LBL\" \"\" \
                --button=\"    Back to main menu   \"'" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span font_desc='CustomFont 10'> </span> Update Mirrors:FBTN" "bash $update_mirrors_script" \
            --field="Rank Mirrors:FBTN" "bash $eos_rankmirrors_script" \
            --field="Repair Keyring:FBTN" "bash $keyring_repair_script" \
            --field="Repair yay:FBTN" "bash $refresh_yay_script" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span font_desc='CustomFont 10'> </span> Trouble Shoot Logs:FBTN" "bash $eos_log_tool_script" \
        --tab="System updates" \
            --form \
            --columns=3 \
            --field="<b><span font_desc='CustomFont 10'></span>   System updates</b>:FBTN" "bash -c 'yad --notebook --tab=\"System updates\" --form \
                --geometry=900x410+100+100 \
                --columns=3 \
                --image=\"$logo_path\" \
                --image-on-top-center \
                --field=\" <b>System Update</b>:FBTN\" \"bash $system_update_script\" \
                --field=\" <b>Full hypr</b> Update:FBTN\" \"bash $kitty_script\" \
                --field=\" <b>Hypr-blizz</b> Update:FBTN\" \"bash $hypr_blizz_update_script\" \
                --field=\" <b>hypr-welcome</b> Update:FBTN\" \"bash $hypr_welcome_update_script\" \
                --field=\" <b>hypr-waybar</b> Update:FBTN\" \"bash $hypr_waybar_update_script\" \
                --field=\"$update_status:FBTN\" \"bash $update_script\" \
                --button=\"   Back to main menu\"'" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="$update_status:FBTN" "bash \"$update_script\"" \
            --field="Switch Your Waybar:FBTN" "bash $waybar_script" \
            --field="Keyhint:FBTN" "bash $hypr_keyhint_script" \
            --field="Maintenance:FBTN" "bash $cleanup_script" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span foreground='white'> </span>                                        :LBL" "" \
            --field="<span font_desc='CustomFont 10'></span>  Hide me forever ...:FBTN" "bash $kill_script" \
        --button-layout=end \
        --no-cancel \
        --no-focus \
        --separator="," \
        --timeout=0 &
}

# Create Yad dialog
create_yad_dialog

# Trap to clean up zombie processes
trap "sleep 0.2 ; $HOME/.config/hypr/scripts/kill_zombies.sh --silent" EXIT

exit 0
