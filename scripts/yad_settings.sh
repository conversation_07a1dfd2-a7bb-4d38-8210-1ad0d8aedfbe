#!/bin/bash

# Path to your custom logo or image
logo_path="$HOME/.config/hypr/imgs/hypr-welcome.png"

# Function to show the main menu
show_main_menu() {
    while true; do
        main_menu="Hyprland\nWaybar configs\nWaybar styles"
        selected_config=$(echo -e "$main_menu" | yad --title="Settings" --text="" --geometry=900x410+800+600 --width=900 --height=410 --fixed --list --column="Settings" --separator='\n' --borders=9 -timeout=5 --button="   Kill me i dare you ..." --center --image="$logo_path")

        # Check if the escape button was pressed or the dialog was closed
        if [ "$selected_config" == "" ]; then
            echo "Escape key pressed or dialog closed. Exiting script."
            exit
        fi

        echo "Selected: $selected_config"

        case $selected_config in
            "Hyprland")
                launch_hyprland_settings ;;
            "Waybar configs")
                launch_waybar_configs ;;
            "Waybar styles")
                launch_waybar_styles ;;
            *)
                ;;
        esac
    done
}

# Function to launch Hyprland settings submenu
launch_hyprland_settings() {
    while true; do
        submenu="Back to main menu\n~/.config/hypr/hyprland.conf\n~/.config/hypr/conf/exec_once.conf\n~/.config/hypr/conf/env_var.conf\n~/.config/hypr/conf/monitor.conf\n~/.config/hypr/conf/workspaces.conf\n~/.config/hypr/conf/key_binds.conf\n~/.config/hypr/conf/window_binds.conf\n~/.config/hypr/conf/window_rules.conf"
        selected_submenu=$(echo -e "$submenu" | yad --title="Hyprland Settings" --text="" --geometry=900x410+800+600 --width=900 --height=410 --fixed --list --column="Settings" --separator='\n' --borders=9 -timeout=5 --button="   Kill me i dare you ..." --center --image="$logo_path") 
        
        # Check if the escape button was pressed or the dialog was close
        if [ "$selected_submenu" == "" ]; then
            echo "Escape key pressed or dialog closed. Exiting script."
            exit
        fi

        case $selected_submenu in
            "Back to main menu")
                break ;;
            *)
                echo "Launching nano for $selected_submenu"
                kitty nano "$selected_submenu" ;;
        esac
    done
}

# Function to launch Waybar configs submenu
launch_waybar_configs() {
    while true; do
        submenu="Back to main menu\n~/.config/waybar/conf/w1-config-desktop.jsonc\n~/.config/waybar/conf/w2-config-laptop.jsonc\n~/.config/waybar/conf/w3-config-desktop.jsonc"
        selected_submenu=$(echo -e "$submenu" | yad --title="Waybar Configs" --text="" --geometry=900x410+800+600 --width=900 --height=410 --fixed --list --column="Configs" --separator='\n' --borders=9 -timeout=5 --button="   Kill me i dare you ..." --center --image="$logo_path")
        
        # Check if the escape button was pressed or the dialog was closed
        if [ "$selected_submenu" == "" ]; then
            echo "Escape key pressed or dialog closed. Exiting script."
            exit
        fi

        case $selected_submenu in
            "Back to main menu")
                break ;;
            *)
                echo "Launching nano for $selected_submenu"
                kitty nano "$selected_submenu" ;;
        esac
    done
}

# Function to launch Waybar styles submenu
launch_waybar_styles() {
    while true; do
        submenu="Back to main menu\n~/.config/waybar/style/w1-style.css\n~/.config/waybar/style/w2-style.css\n~/.config/waybar/style/w3-style.css"
        selected_submenu=$(echo -e "$submenu" | yad --title="Waybar Styles" --text="" --geometry=900x410+800+600 --width=900 --height=410 --fixed --list --column="Styles" --separator='\n' --borders=9 -timeout=5 --button="   Kill me i dare you ..." --center --image="$logo_path")
        
        # Check if the escape button was pressed or the dialog was closed
        if [ "$selected_submenu" == "" ]; then
            echo "Escape key pressed or dialog closed. Exiting script."
            exit
        fi

        case $selected_submenu in
            "Back to main menu")
                break ;;
            *)
                echo "Launching nano for $selected_submenu"
                kitty nano "$selected_submenu" ;;
        esac
    done
}

# Start the main menu loop
show_main_menu
