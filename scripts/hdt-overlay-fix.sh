#!/bin/bash

# ═══════════════════════════════════════════════════════════════
# HDT (Hearthstone Deck Tracker) Overlay Fix Script
# For Hyprland/Wayland with NVIDIA GPU
# ═══════════════════════════════════════════════════════════════

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running on Hyprland
check_hyprland() {
    if [[ "$XDG_CURRENT_DESKTOP" != "Hyprland" ]]; then
        print_error "This script is designed for Hyprland. Current desktop: $XDG_CURRENT_DESKTOP"
        exit 1
    fi
    print_success "Running on Hyprland"
}

# Function to find Bottles prefix
find_bottles_prefix() {
    local bottles_dir="$HOME/.local/share/bottles/bottles"
    local hearthstone_prefix=""
    
    if [[ -d "$bottles_dir" ]]; then
        # Look for Hearthstone bottle
        for bottle in "$bottles_dir"/*; do
            if [[ -d "$bottle" ]] && [[ -f "$bottle/bottle.yml" ]]; then
                if grep -q -i "hearthstone\|blizzard" "$bottle/bottle.yml" 2>/dev/null; then
                    hearthstone_prefix="$bottle"
                    break
                fi
            fi
        done
    fi
    
    echo "$hearthstone_prefix"
}

# Function to apply Wine registry fixes
apply_wine_fixes() {
    local prefix="$1"
    
    if [[ -z "$prefix" || ! -d "$prefix" ]]; then
        print_error "Wine prefix not found: $prefix"
        return 1
    fi
    
    print_status "Applying Wine registry fixes for HDT overlay..."
    
    # Set WINEPREFIX
    export WINEPREFIX="$prefix"
    
    # Disable WPF hardware acceleration (fixes black background)
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" /t REG_DWORD /d 1 /f
    
    # Disable D3D11 and DXGI for better compatibility
    wine reg add "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "d3d11" /t REG_SZ /d "disabled" /f
    wine reg add "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dxgi" /t REG_SZ /d "disabled" /f
    
    # Set Windows version to Windows 10
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion" /v "CurrentVersion" /t REG_SZ /d "10.0" /f
    
    # Disable desktop composition (helps with overlay)
    wine reg add "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dwmapi" /t REG_SZ /d "disabled" /f
    
    print_success "Wine registry fixes applied"
}

# Function to create HDT configuration
create_hdt_config() {
    local config_dir="$HOME/.config/hdt"
    mkdir -p "$config_dir"
    
    cat > "$config_dir/config.xml" << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<Config>
  <!-- Overlay Settings -->
  <OverlayPlayerScaling>100</OverlayPlayerScaling>
  <OverlayOpponentScaling>100</OverlayOpponentScaling>
  <OverlayPlayerOpacity>95</OverlayPlayerOpacity>
  <OverlayOpponentOpacity>95</OverlayOpponentOpacity>
  
  <!-- Prevent auto-hiding -->
  <HideOverlayInBackground>false</HideOverlayInBackground>
  <HideOverlayInMenu>false</HideOverlayInMenu>
  <HideOverlayInSpectator>false</HideOverlayInSpectator>
  
  <!-- Force overlay to stay on top -->
  <OverlayAlwaysOnTop>true</OverlayAlwaysOnTop>
  <OverlayPlayerAlwaysOnTop>true</OverlayPlayerAlwaysOnTop>
  <OverlayOpponentAlwaysOnTop>true</OverlayOpponentAlwaysOnTop>
  
  <!-- Disable hardware acceleration -->
  <DisableHardwareAcceleration>true</DisableHardwareAcceleration>
  
  <!-- Window settings -->
  <OverlayPlayerWindowTop>100</OverlayPlayerWindowTop>
  <OverlayPlayerWindowLeft>50</OverlayPlayerWindowLeft>
  <OverlayOpponentWindowTop>100</OverlayOpponentWindowTop>
  <OverlayOpponentWindowRight>50</OverlayOpponentWindowRight>
</Config>
EOF
    
    print_success "HDT configuration created at $config_dir/config.xml"
}

# Function to reload Hyprland configuration
reload_hyprland() {
    print_status "Reloading Hyprland configuration..."
    hyprctl reload
    print_success "Hyprland configuration reloaded"
}

# Function to show game settings instructions
show_game_instructions() {
    print_status "=== HEARTHSTONE GAME SETTINGS ==="
    echo
    echo "To fix overlay visibility, you need to change Hearthstone to windowed mode:"
    echo
    echo "1. Launch Hearthstone"
    echo "2. Go to Options → Graphics"
    echo "3. Change 'Display Mode' from 'Fullscreen' to 'Windowed'"
    echo "4. Set resolution to your monitor's native resolution"
    echo "5. Apply settings and restart Hearthstone"
    echo
    echo "Why this helps:"
    echo "- Fullscreen mode on Wayland can block overlays"
    echo "- Windowed mode allows proper overlay rendering"
    echo "- Performance impact is minimal on modern systems"
    echo
}

# Function to show HDT settings instructions
show_hdt_instructions() {
    print_status "=== HDT APPLICATION SETTINGS ==="
    echo
    echo "In Hearthstone Deck Tracker settings:"
    echo
    echo "1. Go to Options → Overlay"
    echo "2. Enable 'Player Deck' and 'Opponent Deck'"
    echo "3. Set opacity to 95% (or adjust as needed)"
    echo "4. Enable 'Always on top'"
    echo "5. Disable 'Hide in background'"
    echo "6. Disable 'Hide in menu'"
    echo
    echo "Advanced settings:"
    echo "1. Go to Options → General"
    echo "2. Enable 'Start with Windows' if desired"
    echo "3. Disable hardware acceleration if available"
    echo
}

# Function to test overlay
test_overlay() {
    print_status "Testing overlay functionality..."
    
    # Check if HDT process is running
    if pgrep -f "HearthstoneDeckTracker" > /dev/null; then
        print_success "HDT is running"
    else
        print_warning "HDT is not currently running"
    fi
    
    # Check if Hearthstone is running
    if pgrep -f "Hearthstone" > /dev/null; then
        print_success "Hearthstone is running"
    else
        print_warning "Hearthstone is not currently running"
    fi
    
    # Check window rules
    print_status "Checking Hyprland window rules..."
    if hyprctl clients | grep -i "hearthstone\|hdt" > /dev/null; then
        print_success "Game/HDT windows detected by Hyprland"
        hyprctl clients | grep -i "hearthstone\|hdt"
    else
        print_warning "No game/HDT windows currently detected"
    fi
}

# Main execution
main() {
    echo "═══════════════════════════════════════════════════════════════"
    echo "HDT (Hearthstone Deck Tracker) Overlay Fix Script"
    echo "For Hyprland/Wayland with NVIDIA GPU"
    echo "═══════════════════════════════════════════════════════════════"
    echo
    
    check_hyprland
    
    # Find Bottles prefix
    bottles_prefix=$(find_bottles_prefix)
    if [[ -n "$bottles_prefix" ]]; then
        print_success "Found Hearthstone Bottles prefix: $bottles_prefix"
        apply_wine_fixes "$bottles_prefix"
    else
        print_warning "Hearthstone Bottles prefix not found automatically"
        print_warning "Please run this script with the prefix path as argument:"
        print_warning "$0 /path/to/your/bottles/prefix"
    fi
    
    # Create HDT configuration
    create_hdt_config
    
    # Reload Hyprland
    reload_hyprland
    
    # Show instructions
    show_game_instructions
    show_hdt_instructions
    
    # Test overlay
    test_overlay
    
    echo
    print_success "HDT overlay fix script completed!"
    echo
    echo "Next steps:"
    echo "1. Restart HDT and Hearthstone"
    echo "2. Change Hearthstone to windowed mode"
    echo "3. Configure HDT overlay settings as shown above"
    echo "4. Test the overlay in a game"
    echo
}

# Check if prefix provided as argument
if [[ $# -eq 1 ]]; then
    apply_wine_fixes "$1"
    create_hdt_config
    reload_hyprland
    show_game_instructions
    show_hdt_instructions
    test_overlay
else
    main
fi
