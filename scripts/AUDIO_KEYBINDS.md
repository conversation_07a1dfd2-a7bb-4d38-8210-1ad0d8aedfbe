# Audio Output Switching Keybinds

## Numpad Keybinds (with SUPER + SHIFT)

- **Numpad 1** (`$mainMod SHIFT, KP_End`): Switch to **USB SPDIF**
- **Numpad 2** (`$mainMod SHIFT, KP_Down`): Switch to **Arctis Nova Pro Wireless**  
- **Numpad 3** (`$mainMod SHIFT, KP_Next`): Switch to **USB Headphones**
- **Numpad 4** (`$mainMod SHIFT, KP_Left`): Switch to **USB 5.1 Speakers**
- **Numpad 5** (`$mainMod SHIFT, KP_Begin`): Show **current output**

## Available Audio Outputs

From your `pactl list short sinks`:

1. **USB SPDIF** - `alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__SPDIF__sink`
2. **USB Headphones** - `alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Headphones__sink`  
3. **USB 5.1 Speakers** - `alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Speaker__sink`
4. **Arctis Nova Pro Wireless** - `alsa_output.usb-SteelSeries_Arctis_Nova_Pro_Wireless-00.analog-stereo`

## Manual Script Usage

You can also use the script directly:

```bash
# Switch to specific output
~/.config/hypr/scripts/audio-switch.sh arctis
~/.config/hypr/scripts/audio-switch.sh spdif
~/.config/hypr/scripts/audio-switch.sh headphones
~/.config/hypr/scripts/audio-switch.sh speakers

# Show current output
~/.config/hypr/scripts/audio-switch.sh current

# List all options
~/.config/hypr/scripts/audio-switch.sh list
```

## Features

- **Automatic stream moving**: All currently playing audio streams are moved to the new output
- **Desktop notifications**: Shows which output was selected
- **Error handling**: Notifies if output device is not available
- **Current output display**: Shows which output is currently active

## Troubleshooting

If an output doesn't work:
1. Check if the device is connected: `pactl list short sinks`
2. Test the script manually: `~/.config/hypr/scripts/audio-switch.sh list`
3. Check Hyprland logs for keybind issues
