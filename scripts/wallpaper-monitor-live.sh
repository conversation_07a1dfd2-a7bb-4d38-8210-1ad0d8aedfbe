#!/bin/bash

# Real-time wallpaper script monitor
LOGFILE="$HOME/.local/share/random-wallpaper.log"
LOCKFILE="/tmp/random-wallpaper.lock"

echo "🔍 Real-time Random Wallpaper Monitor"
echo "====================================="
echo "Press Ctrl+C to stop monitoring"
echo "Time: $(date)"
echo

# Function to get colored status
get_status() {
    if [[ -f "$LOCKFILE" ]]; then
        local pid=$(cat "$LOCKFILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "\033[32m✅ RUNNING (PID: $pid)\033[0m"
        else
            echo -e "\033[31m❌ DEAD (stale lockfile)\033[0m"
        fi
    else
        echo -e "\033[31m❌ NOT RUNNING\033[0m"
    fi
}

# Function to get swww status
get_swww_status() {
    if swww query >/dev/null 2>&1; then
        echo -e "\033[32m✅ RUNNING\033[0m"
    else
        echo -e "\033[31m❌ NOT RUNNING\033[0m"
    fi
}

# Monitor loop
previous_status=""
error_count=0
last_error_count=0

while true; do
    clear
    echo "🔍 Real-time Random Wallpaper Monitor"
    echo "====================================="
    echo "$(date)"
    echo
    
    # Current status
    current_status=$(get_status)
    echo "Script Status: $current_status"
    echo "swww Status:   $(get_swww_status)"
    
    # Check for status changes
    if [[ "$current_status" != "$previous_status" ]]; then
        echo -e "\033[33m⚠️  STATUS CHANGED!\033[0m"
        previous_status="$current_status"
    fi
    
    echo
    
    # Error tracking
    if [[ -f "$LOGFILE" ]]; then
        error_count=$(grep -c "ERROR:" "$LOGFILE" 2>/dev/null || echo 0)
        crash_count=$(grep -c "crashed or exited unexpectedly" "$LOGFILE" 2>/dev/null || echo 0)
        
        echo "📊 Statistics:"
        echo "  Errors: $error_count"
        echo "  Crashes: $crash_count"
        
        # Check for new errors
        if [[ $error_count -gt $last_error_count ]]; then
            echo -e "\033[31m🚨 NEW ERROR DETECTED!\033[0m"
            last_error_count=$error_count
        fi
        
        echo
        echo "📝 Last 5 log entries:"
        echo "======================"
        tail -5 "$LOGFILE" 2>/dev/null | while read line; do
            if echo "$line" | grep -q "ERROR:"; then
                echo -e "\033[31m$line\033[0m"
            elif echo "$line" | grep -q "Successfully"; then
                echo -e "\033[32m$line\033[0m"
            else
                echo "$line"
            fi
        done
    else
        echo "📝 No log file found yet"
    fi
    
    echo
    echo "🔄 Auto-refresh in 5 seconds... (Ctrl+C to stop)"
    sleep 5
done
