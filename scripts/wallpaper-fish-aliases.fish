# Wallpaper script debugging aliases for fish shell
# Add this to your ~/.config/fish/config.fish or source it manually

# Main commands
alias wallpaper-status="/home/<USER>/.config/hypr/scripts/wallpaper-status.sh"
alias wallpaper-dashboard="/home/<USER>/.config/hypr/scripts/wallpaper-dashboard.sh"
alias wallpaper-analyze="/home/<USER>/.config/hypr/scripts/analyze-wallpaper-logs.sh"
alias wallpaper-logs="tail -f ~/.local/share/random-wallpaper.log"

# Systemd service management
alias wallpaper-start="systemctl --user start random-wallpaper.service"
alias wallpaper-stop="systemctl --user stop random-wallpaper.service"
alias wallpaper-restart="systemctl --user restart random-wallpaper.service"
alias wallpaper-enable="systemctl --user enable random-wallpaper.service"
alias wallpaper-disable="systemctl --user disable random-wallpaper.service"
alias wallpaper-journal="journalctl --user -u random-wallpaper.service -f"

# Manual script management
alias wallpaper-kill="pkill -f random-wallpaper"
alias wallpaper-run="/home/<USER>/.config/hypr/scripts/random-wallpaper &"

# Monitor and debugging
alias wallpaper-monitor="/home/<USER>/.config/hypr/scripts/monitor-wallpaper.sh &"
alias wallpaper-check="ps aux | grep random-wallpaper | grep -v grep"

# Audio output switching aliases
alias audio-arctis="/home/<USER>/.config/hypr/scripts/audio-switch.sh arctis"
alias audio-spdif="/home/<USER>/.config/hypr/scripts/audio-switch.sh spdif"
alias audio-headphones="/home/<USER>/.config/hypr/scripts/audio-switch.sh headphones"
alias audio-speakers="/home/<USER>/.config/hypr/scripts/audio-switch.sh speakers"
alias audio-current="/home/<USER>/.config/hypr/scripts/audio-switch.sh current"
alias audio-list="/home/<USER>/.config/hypr/scripts/audio-switch.sh list"

# Quick functions
function wallpaper-help
    echo "🖼️  Random Wallpaper Debug Commands:"
    echo "=================================="
    echo "📊 Status & Monitoring:"
    echo "  wallpaper-status     - Quick status check"
    echo "  wallpaper-dashboard  - Full dashboard view"
    echo "  wallpaper-analyze    - Analyze crash logs"
    echo "  wallpaper-logs       - Live log viewing"
    echo "  wallpaper-check      - Check if running"
    echo ""
    echo "🔧 Service Management (Recommended):"
    echo "  wallpaper-start      - Start systemd service"
    echo "  wallpaper-stop       - Stop systemd service"
    echo "  wallpaper-restart    - Restart systemd service"
    echo "  wallpaper-journal    - View systemd logs"
    echo ""
    echo "🛠️  Manual Management:"
    echo "  wallpaper-run        - Start script manually"
    echo "  wallpaper-kill       - Kill running script"
    echo "  wallpaper-monitor    - Start crash monitor"
    echo ""
    echo "💡 TIP: Use 'wallpaper-dashboard' for a complete overview!"
end

function wallpaper-crash-info
    set crash_files (ls ~/.local/share/wallpaper-crash-*.log 2>/dev/null)
    if test (count $crash_files) -gt 0
        echo "🚨 Found crash dump files:"
        for file in $crash_files
            echo "  - $file"
            echo "    Created: "(stat -c %y "$file")
        end
        echo ""
        echo "Latest crash dump:"
        cat $crash_files[-1]
    else
        echo "✅ No crash dump files found"
    end
end

# Quick audio switching function
function audio-help
    echo "🔊 Audio Output Switching:"
    echo "========================="
    echo "Manual commands:"
    echo "  audio-arctis     - Switch to Arctis Nova Pro Wireless"
    echo "  audio-spdif      - Switch to USB SPDIF"
    echo "  audio-headphones - Switch to USB Headphones"
    echo "  audio-speakers   - Switch to USB 5.1 Speakers"
    echo "  audio-current    - Show current output"
    echo "  audio-list       - List all options"
    echo ""
    echo "Keybinds (SUPER + SHIFT + Numpad):"
    echo "  Numpad 1 - USB SPDIF"
    echo "  Numpad 2 - Arctis Pro Wireless"
    echo "  Numpad 3 - USB Headphones"
    echo "  Numpad 4 - USB 5.1 Speakers"
    echo "  Numpad 5 - Show current output"
end
