# Random Wallpaper Script Debugging Guide

## 🔧 Debugging Tools Created

Your wallpaper script now has comprehensive debugging and monitoring capabilities. Here are all the tools available:

### 📊 Status & Monitoring Scripts

1. **Dashboard** - Complete overview
   ```bash
   /home/<USER>/.config/hypr/scripts/wallpaper-dashboard.sh
   ```

2. **Status Checker** - Quick status check
   ```bash
   /home/<USER>/.config/hypr/scripts/wallpaper-status.sh
   ```

3. **Log Analyzer** - Detailed crash analysis
   ```bash
   /home/<USER>/.config/hypr/scripts/analyze-wallpaper-logs.sh
   ```

4. **Live Monitor** - Real-time monitoring (run in separate terminal)
   ```bash
   /home/<USER>/.config/hypr/scripts/wallpaper-monitor-live.sh
   ```

5. **Background Monitor** - Automatic restart on crashes
   ```bash
   /home/<USER>/.config/hypr/scripts/monitor-wallpaper.sh &
   ```

### 🔧 Service Management (Recommended)

The script is now set up as a systemd user service for automatic restarts:

- **Start**: `systemctl --user start random-wallpaper.service`
- **Stop**: `systemctl --user stop random-wallpaper.service`
- **Restart**: `systemctl --user restart random-wallpaper.service`
- **Status**: `systemctl --user status random-wallpaper.service`
- **Logs**: `journalctl --user -u random-wallpaper.service -f`
- **Enable** (auto-start): `systemctl --user enable random-wallpaper.service` ✅ Already enabled
- **Disable**: `systemctl --user disable random-wallpaper.service`

### 📝 Log Files

- **Main log**: `~/.local/share/random-wallpaper.log`
- **Monitor log**: `~/.local/share/wallpaper-monitor.log`
- **Crash dumps**: `~/.local/share/wallpaper-crash-*.log`

### 🐟 Fish Shell Aliases

Source the aliases file for convenient commands:
```fish
source /home/<USER>/.config/hypr/scripts/wallpaper-fish-aliases.fish
```

Then you can use:
- `wallpaper-status` - Quick status
- `wallpaper-dashboard` - Full dashboard
- `wallpaper-start` - Start service
- `wallpaper-stop` - Stop service
- `wallpaper-logs` - Live log viewing
- `wallpaper-help` - Show all commands

## 🚨 How to Debug Crashes

### 1. Real-time Monitoring
Run this in a separate terminal to watch for crashes:
```bash
/home/<USER>/.config/hypr/scripts/wallpaper-monitor-live.sh
```

### 2. Check Current Status
```bash
/home/<USER>/.config/hypr/scripts/wallpaper-dashboard.sh
```

### 3. Analyze Crash Patterns
```bash
/home/<USER>/.config/hypr/scripts/analyze-wallpaper-logs.sh
```

### 4. View Live Logs
```bash
tail -f ~/.local/share/random-wallpaper.log
```

### 5. Check Systemd Service
```bash
systemctl --user status random-wallpaper.service
journalctl --user -u random-wallpaper.service -f
```

## 🔍 What Each Tool Shows

### Dashboard Output Includes:
- ✅/❌ Script running status
- ✅/❌ swww daemon status  
- 📝 Last log entry
- 🚨 Error count and recent errors
- 💾 System resources (memory/disk)
- 🔧 Quick command reference

### Log Analyzer Shows:
- 📈 Statistics (errors, crashes, restarts)
- 🔥 Recent errors (last 10)
- 💥 Crash details with context
- 🕒 Recent activity
- 🖥️ System state during crashes
- 🔍 Pattern analysis
- 🎯 Recommendations

### Live Monitor Displays:
- 🔍 Real-time status updates
- 📊 Error count tracking
- 🚨 Alerts for new errors/status changes
- 📝 Last 5 log entries with color coding

## 🎯 Enhanced Crash Detection

The script now includes:
- **Error trapping** - Catches errors with line numbers
- **Crash dumps** - Detailed system state at crash time
- **Heartbeat logging** - Regular "alive" messages
- **System resource logging** - Memory/disk usage
- **Signal handling** - Graceful cleanup on termination
- **Automatic restart** via systemd
- **Lockfile management** - Prevents multiple instances

## 📋 Current Status

✅ **Service is running and working correctly**
✅ **Systemd service enabled for auto-restart**
✅ **All debugging tools installed**
✅ **Enhanced logging active**

## 🚀 Quick Start Commands

### To check if everything is working:
```bash
/home/<USER>/.config/hypr/scripts/wallpaper-dashboard.sh
```

### To monitor in real-time:
```bash
/home/<USER>/.config/hypr/scripts/wallpaper-monitor-live.sh
```

### To view live logs:
```bash
tail -f ~/.local/share/random-wallpaper.log
```

## 🛟 Emergency Commands

If the script gets stuck or behaves unexpectedly:

1. **Kill all instances**: `pkill -f random-wallpaper`
2. **Restart service**: `systemctl --user restart random-wallpaper.service`
3. **Check logs**: `journalctl --user -u random-wallpaper.service -n 50`
4. **Remove lockfile**: `rm -f /tmp/random-wallpaper.lock`

The script should now be much more stable and provide detailed information about any crashes or issues!
