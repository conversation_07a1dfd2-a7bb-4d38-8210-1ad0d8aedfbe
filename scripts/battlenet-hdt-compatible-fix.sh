#!/bin/bash

# ═══════════════════════════════════════════════════════════════
# Battle.net + HDT Compatible Fix Script
# Fixes HDT overlay issues without breaking Battle.net
# ═══════════════════════════════════════════════════════════════

set -e

WINEPREFIX="/home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test Battle.net functionality
test_battlenet() {
    print_status "Testing Battle.net functionality..."
    
    export WINEPREFIX
    
    # Check if Battle.net executable exists
    if [[ -f "$WINEPREFIX/drive_c/Program Files (x86)/Battle.net/Battle.net Launcher.exe" ]]; then
        print_success "Battle.net executable found"
        
        # Try to run Battle.net in test mode (just check if it starts)
        print_status "Testing Battle.net startup..."
        timeout 10s wine "$WINEPREFIX/drive_c/Program Files (x86)/Battle.net/Battle.net Launcher.exe" --exec="exit" 2>/dev/null || true
        
        if [[ $? -eq 0 ]]; then
            print_success "Battle.net can start successfully"
        else
            print_warning "Battle.net may have startup issues"
        fi
    else
        print_warning "Battle.net not installed yet"
    fi
}

# Apply HDT-specific fixes only
apply_hdt_fixes() {
    print_status "Applying HDT-specific overlay fixes..."
    
    export WINEPREFIX
    
    # Core HDT fixes that don't affect Battle.net
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" /t REG_DWORD /d 1 /f
    
    # HDT application-specific settings
    wine reg add "HKEY_CURRENT_USER\Software\HearthstoneDeckTracker" /v "DisableHardwareAcceleration" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_CURRENT_USER\Software\HearthstoneDeckTracker" /v "AlwaysOnTop" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_CURRENT_USER\Software\HearthstoneDeckTracker" /v "OverlayOpacity" /t REG_DWORD /d 95 /f
    
    # Windows Forms hardware acceleration (affects HDT UI)
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework\Windows Forms" /v "DisableHardwareAcceleration" /t REG_DWORD /d 1 /f
    
    # WPF rendering settings
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\Avalon.Graphics" /v "DisableHWAcceleration" /t REG_DWORD /d 1 /f
    
    print_success "HDT overlay fixes applied"
}

# Apply Battle.net compatibility fixes
apply_battlenet_fixes() {
    print_status "Applying Battle.net compatibility fixes..."
    
    export WINEPREFIX
    
    # Windows 10 compatibility (helps with both apps)
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion" /v "CurrentVersion" /t REG_SZ /d "10.0" /f
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion" /v "CurrentBuild" /t REG_SZ /d "19041" /f
    
    # TLS settings for Battle.net authentication
    wine reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" /v "Enabled" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" /v "DisabledByDefault" /t REG_DWORD /d 0 /f
    
    # Internet Explorer settings for web authentication
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\Internet Explorer\Main" /v "Start Page" /t REG_SZ /d "about:blank" /f
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\Internet Explorer\Main" /v "Use FormSuggest" /t REG_SZ /d "no" /f
    
    # Battle.net specific settings (non-breaking)
    wine reg add "HKEY_CURRENT_USER\Software\Blizzard Entertainment\Battle.net" /v "AllowBetaClient" /t REG_DWORD /d 0 /f
    
    print_success "Battle.net compatibility fixes applied"
}

# Remove problematic DLL overrides
remove_problematic_overrides() {
    print_status "Removing problematic DLL overrides..."
    
    export WINEPREFIX
    
    # Remove DLL overrides that break Battle.net
    wine reg delete "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "d3d11" /f 2>/dev/null || true
    wine reg delete "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dxgi" /f 2>/dev/null || true
    wine reg delete "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dwmapi" /f 2>/dev/null || true
    
    print_success "Problematic DLL overrides removed"
}

# Main execution
main() {
    echo "═══════════════════════════════════════════════════════════════"
    echo "Battle.net + HDT Compatible Fix Script"
    echo "═══════════════════════════════════════════════════════════════"
    echo
    
    if [[ ! -d "$WINEPREFIX" ]]; then
        print_error "Wine prefix not found: $WINEPREFIX"
        exit 1
    fi
    
    print_success "Wine prefix found: $WINEPREFIX"
    echo
    
    # Remove problematic settings first
    remove_problematic_overrides
    
    # Apply compatible fixes
    apply_battlenet_fixes
    apply_hdt_fixes
    
    # Test Battle.net
    test_battlenet
    
    echo
    print_success "Compatible fixes applied successfully!"
    echo
    echo "🎯 What's been fixed:"
    echo "✅ HDT overlay transparency (WPF hardware acceleration disabled)"
    echo "✅ HDT overlay stability (Windows Forms acceleration disabled)"
    echo "✅ Battle.net compatibility (DLL overrides removed)"
    echo "✅ Windows 10 compatibility for both applications"
    echo "✅ TLS 1.2 support for Battle.net authentication"
    echo
    echo "🚀 Next steps:"
    echo "1. Launch Battle.net - it should work without DLL errors"
    echo "2. Use web browser login if needed"
    echo "3. Launch Hearthstone in WINDOWED mode"
    echo "4. Launch HDT and test overlay"
    echo
}

main
