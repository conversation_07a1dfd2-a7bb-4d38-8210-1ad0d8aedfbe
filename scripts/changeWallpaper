#!/bin/bash

# Directory for wallpapers
DIR=$HOME/Pictures/wallpapers-redblizard/

# Ensure swww is running
swww query || swww init

# Randomly select a wallpaper
PICS=($(find ${DIR} -type f -name "*.jpg" -o -name "*.png" -o -name "*.gif"))
RANDOMPICS=${PICS[$RANDOM % ${#PICS[@]}]}

# Change wallpaper using swww with transition effects
swww img "$RANDOMPICS" --transition-type wipe --transition-angle 30 --transition-step 120 --transition-fps 60

# Copy the selected wallpaper to $HOME/.cache/swww as current_wallpaper.jpg
cp "$RANDOMPICS" $HOME/.cache/swww/current_wallpaper.png

# Generate color scheme with pywal
wal -c
wal -i "$RANDOMPICS"

# Uncomment the following line if you use Firefox with pywalfox extension
# pywalfox update

# Optionally clear the swww cache (comment out if not needed)
# rm -rf $HOME/.cache/swww
