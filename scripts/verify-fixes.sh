#!/bin/bash

# ═══════════════════════════════════════════════════════════════
# Verify HDT and Battle.net Fixes Applied
# ═══════════════════════════════════════════════════════════════

WINEPREFIX="/home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A"

echo "═══════════════════════════════════════════════════════════════"
echo "Verifying HDT Overlay and Battle.net Fixes"
echo "═══════════════════════════════════════════════════════════════"
echo

# Check if Wine prefix exists
if [[ ! -d "$WINEPREFIX" ]]; then
    echo "❌ Wine prefix not found: $WINEPREFIX"
    exit 1
fi

echo "✅ Wine prefix found: $WINEPREFIX"
echo

# Check registry values
echo "🔍 Checking registry fixes..."

export WINEPREFIX

# Check .NET Framework hardware acceleration
echo -n "  • .NET Framework HW Acceleration disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

# Check DLL overrides
echo -n "  • D3D11 disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "d3d11" 2>/dev/null | grep -q "disabled"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

echo -n "  • DXGI disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Wine\DllOverrides" /v "dxgi" 2>/dev/null | grep -q "disabled"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

# Check Windows version
echo -n "  • Windows 10 compatibility: "
if wine reg query "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion" /v "CurrentVersion" 2>/dev/null | grep -q "10.0"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

# Check Battle.net settings
echo -n "  • Battle.net HW Acceleration disabled: "
if wine reg query "HKEY_CURRENT_USER\Software\Blizzard Entertainment\Battle.net" /v "DisableHardwareAcceleration" 2>/dev/null | grep -q "0x1"; then
    echo "✅ YES"
else
    echo "❌ NO"
fi

echo

# Check Hyprland window rules
echo "🔍 Checking Hyprland window rules..."
if grep -q "HDT.*Overlay" ~/.config/hypr/conf/window_rules.conf; then
    echo "✅ HDT overlay window rules found"
else
    echo "❌ HDT overlay window rules missing"
fi

# Check environment variables
echo
echo "🔍 Checking environment variables..."
if grep -q "DOTNET_SYSTEM_WINDOWS_MEDIA_DISABLEHARDWAREACCELERATION" ~/.config/hypr/conf/env_var.conf; then
    echo "✅ WPF hardware acceleration disabled in env vars"
else
    echo "❌ WPF hardware acceleration env vars missing"
fi

if grep -q "WINEDLLOVERRIDES.*d3d11=disabled" ~/.config/hypr/conf/env_var.conf; then
    echo "✅ Wine DLL overrides found in env vars"
else
    echo "❌ Wine DLL overrides missing in env vars"
fi

echo
echo "═══════════════════════════════════════════════════════════════"
echo "Verification Complete!"
echo "═══════════════════════════════════════════════════════════════"
echo
echo "🎯 Next Steps:"
echo "1. Launch Battle.net from Bottles"
echo "2. Use 'Web browser login' if login issues persist"
echo "3. Launch Hearthstone in WINDOWED mode (not fullscreen)"
echo "4. Launch HDT and configure overlay settings"
echo "5. Test overlay in-game"
echo
