#!/bin/bash

# Quick status checker for random-wallpaper
LOCKFILE="/tmp/random-wallpaper.lock"
LOGFILE="$HOME/.local/share/random-wallpaper.log"

echo "🖼️  Random Wallpaper Status Check"
echo "================================="
echo "Time: $(date)"
echo

# Check if script is running
if [[ -f "$LOCKFILE" ]]; then
    pid=$(cat "$LOCKFILE")
    if kill -0 "$pid" 2>/dev/null; then
        echo "✅ Script is RUNNING (PID: $pid)"
        
        # Show how long it's been running
        if command -v ps >/dev/null; then
            runtime=$(ps -o etime= -p "$pid" 2>/dev/null | tr -d ' ')
            echo "   Runtime: $runtime"
        fi
    else
        echo "❌ Script is NOT running (stale lockfile)"
    fi
else
    echo "❌ Script is NOT running (no lockfile)"
fi

# Check swww status
echo
echo "🎨 swww Status:"
if swww query >/dev/null 2>&1; then
    echo "✅ swww is running"
    current_wallpaper=$(swww query | head -1 || echo "unknown")
    echo "   Current: $current_wallpaper"
else
    echo "❌ swww is not running"
fi

# Check last log entry
echo
echo "📝 Last Log Entry:"
if [[ -f "$LOGFILE" ]]; then
    last_entry=$(tail -1 "$LOGFILE")
    echo "$last_entry"
    
    # Check how recent it is
    if echo "$last_entry" | grep -q "$(date '+%Y-%m-%d')"; then
        echo "✅ Recent activity (today)"
    else
        echo "⚠️  No recent activity"
    fi
else
    echo "❌ No log file found"
fi

# Quick error check
echo
echo "🚨 Recent Errors:"
if [[ -f "$LOGFILE" ]] && grep -q "ERROR:" "$LOGFILE"; then
    error_count=$(grep -c "ERROR:" "$LOGFILE")
    echo "   Found $error_count total errors"
    echo "   Last error:"
    grep "ERROR:" "$LOGFILE" | tail -1
else
    echo "✅ No errors in logs"
fi

# Resource check
echo
echo "💾 System Resources:"
echo "   Memory: $(free -h | grep Mem: | awk '{print $3 "/" $2}')"
echo "   Disk (/home): $(df -h "$HOME" | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"

echo
echo "🔧 Quick Commands:"
echo "   Start script: /home/<USER>/.config/hypr/scripts/random-wallpaper &"
echo "   Stop script:  pkill -f random-wallpaper"
echo "   View logs:    tail -f $LOGFILE"
echo "   Analyze:      /home/<USER>/.config/hypr/scripts/analyze-wallpaper-logs.sh"
