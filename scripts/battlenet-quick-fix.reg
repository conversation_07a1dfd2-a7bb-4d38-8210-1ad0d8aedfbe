Windows Registry Editor Version 5.00

; ═══════════════════════════════════════════════════════════════
; Battle.net Quick Login Fix Registry Settings
; ═══════════════════════════════════════════════════════════════

; Set Windows version to Windows 10 for better compatibility
[HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion]
"CurrentVersion"="10.0"
"CurrentBuild"="19041"
"CurrentBuildNumber"="19041"
"ProductName"="Windows 10 Pro"

; Enable TLS 1.2 and 1.3 for secure connections
[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client]
"Enabled"=dword:00000001
"DisabledByDefault"=dword:00000000

[HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Client]
"Enabled"=dword:00000001
"DisabledByDefault"=dword:00000000

; Internet Explorer settings for authentication
[HKEY_CURRENT_USER\Software\Microsoft\Internet Explorer\Main]
"Start Page"="about:blank"
"Use FormSuggest"="no"
"FormSuggest Passwords"="no"
"FormSuggest PW Ask"="no"

; Disable IE Enhanced Security Configuration
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Active Setup\Installed Components\{A509B1A7-37EF-4b3f-8CFC-4F3A74704073}]
"IsInstalled"=dword:00000000

[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Active Setup\Installed Components\{A509B1A8-37EF-4b3f-8CFC-4F3A74704073}]
"IsInstalled"=dword:00000000

; Battle.net specific settings
[HKEY_CURRENT_USER\Software\Blizzard Entertainment\Battle.net]
"AllowBetaClient"=dword:00000000
"DisableHardwareAcceleration"=dword:00000001
"UseWebAuth"=dword:00000001

; WinHTTP settings for better web connectivity
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Internet Settings\WinHttp]
"DefaultSecureProtocols"=dword:00000aa0

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Internet Settings]
"SecureProtocols"=dword:00000aa0

; Disable Windows Defender (can interfere with authentication)
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows Defender]
"DisableAntiSpyware"=dword:00000001

; Certificate store settings
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\SystemCertificates\ROOT\Certificates]

; User Agent string for better compatibility
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Internet Settings\User Agent\Post Platform]
"Win64; x64"=""

; Disable proxy auto-detection that can cause login issues
[HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Internet Settings]
"AutoDetect"=dword:00000000
"ProxyEnable"=dword:00000000
