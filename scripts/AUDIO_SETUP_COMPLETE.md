# 🔊 Audio Output Switching - Setup Complete!

## ✅ What's Been Configured

### 🎯 Keybinds Added to `key_binds.conf`:
- **SUPER + SHIFT + Numpad 1**: Switch to USB SPDIF
- **SUPER + SHIFT + Numpad 2**: Switch to Arctis Nova Pro Wireless  
- **SUPER + SHIFT + Numpad 3**: Switch to USB Headphones
- **SUPER + SHIFT + Numpad 4**: Switch to USB 5.1 Speakers
- **SUPER + SHIFT + Numpad 5**: Show current audio output

### 📁 Files Created/Modified:
1. **`/home/<USER>/.config/hypr/scripts/audio-switch.sh`** - Main switching script
2. **`/home/<USER>/.config/hypr/conf/key_binds.conf`** - Added keybinds
3. **`/home/<USER>/.config/hypr/scripts/AUDIO_KEYBINDS.md`** - Reference guide
4. **`/home/<USER>/.config/hypr/scripts/wallpaper-fish-aliases.fish`** - Added audio aliases

## 🎮 How to Use

### Via Keybinds (Recommended):
- Hold **SUPER + SHIFT** and press the corresponding numpad key
- You'll get a desktop notification showing the switch
- All current audio streams automatically move to the new output

### Via Terminal/Fish Aliases:
```fish
# Quick commands (after sourcing aliases)
audio-arctis     # Switch to Arctis
audio-spdif      # Switch to SPDIF
audio-headphones # USB Headphones
audio-speakers   # 5.1 Speakers
audio-current    # Show current
audio-help       # Show help
```

### Via Script Directly:
```bash
~/.config/hypr/scripts/audio-switch.sh arctis
~/.config/hypr/scripts/audio-switch.sh spdif
~/.config/hypr/scripts/audio-switch.sh current
```

## 🔧 Available Audio Outputs

From your system (`pactl list short sinks`):

| Output | Device Name | Keybind |
|--------|-------------|---------|
| **USB SPDIF** | `alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__SPDIF__sink` | Numpad 1 |
| **Arctis Nova Pro Wireless** | `alsa_output.usb-SteelSeries_Arctis_Nova_Pro_Wireless-00.analog-stereo` | Numpad 2 |
| **USB Headphones** | `alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Headphones__sink` | Numpad 3 |
| **USB 5.1 Speakers** | `alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Speaker__sink` | Numpad 4 |

## ✨ Features

- **🔄 Automatic Stream Moving**: All playing audio moves to new output
- **📢 Desktop Notifications**: Visual feedback when switching
- **🛡️ Error Handling**: Graceful handling of disconnected devices  
- **💡 Current Status**: Show which output is active
- **🎯 Smart Detection**: Validates device availability before switching

## 🧪 Testing Status

✅ **Script created and executable**  
✅ **Keybinds added to Hyprland config**  
✅ **Configuration reloaded successfully**  
✅ **All devices detected by PipeWire**  
✅ **Switch to Arctis tested and working**  
✅ **Switch to SPDIF tested and working**  
✅ **Desktop notifications working**  
✅ **Current output detection working**  

## 🐠 Fish Shell Integration

To use the convenient aliases, add this to your `~/.config/fish/config.fish`:

```fish
source /home/<USER>/.config/hypr/scripts/wallpaper-fish-aliases.fish
```

Then you can use commands like `audio-arctis`, `audio-spdif`, etc.

## 🚨 Troubleshooting

### If a keybind doesn't work:
1. Check if device is connected: `pactl list short sinks`
2. Test script manually: `~/.config/hypr/scripts/audio-switch.sh arctis`
3. Reload Hyprland: `hyprctl reload`

### If no notification appears:
- Check if `notify-send` is installed: `which notify-send`
- Test manually: `notify-send "Test" "Audio switching"`

### If audio doesn't switch:
1. Verify PipeWire is running: `systemctl --user status pipewire`
2. Check for errors: `journalctl --user -u pipewire`

## 🎯 Quick Reference Card

```
SUPER + SHIFT + Numpad Keys:
┌─────┬─────┬─────┐
│  1  │  2  │  3  │
│SPDIF│ARCT.│HEAD.│
├─────┼─────┼─────┤
│  4  │  5  │     │
│SPKR.│INFO │     │
└─────┴─────┴─────┘
```

**Everything is set up and ready to use!** 🎉
