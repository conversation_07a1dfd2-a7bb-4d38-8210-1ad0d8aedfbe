#!/bin/bash

# Define color codes
RED='\033[0;31m'
BLUE='\033[1;34m'
GREEN='\033[38;2;149;209;137m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if required commands are available
check_dependencies() {
    local missing_deps=()
    local deps=("git" "rsync" "systemctl")
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${RED}Error: Missing required dependencies: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}Please install the missing dependencies and try again.${NC}"
        exit 1
    fi
}

# Check dependencies first
check_dependencies

# Function to launch an Alacritty terminal if not already launched
launch_alacritty_terminal() {
    if [ -z "$ALACRITTY_WINDOW_ID" ]; then
        if ! alacritty -e "$0" &>/dev/null; then
            echo "Failed to launch Alacritty. Please check your Alacritty installation or configuration."
            exit 1
        fi
        exit
    fi
}

# Call the function to launch Alacritty terminal
# launch_alacritty_terminal

# Set the log file path
log_file="$HOME/dotfiles-update_log.txt"

# Redirect stdout (1) and stderr (2) to the log file
exec > >(tee -i "$log_file") 2>&1

# Making a backup of main configs in .config
username=$(whoami)
echo "Now we are making a backup of existing configurations!"
echo

backup_dir="/home/<USER>/.config/backup"
mkdir -p "$backup_dir"

backup() {
    local source_dir="$1"
    local dest_dir="$2"
    mkdir -p "$dest_dir"
    cp -r "$source_dir" "$dest_dir"
}

folders=("alacritty" "btop" "cava" "dunst" "hypr" "kitty" "Kvantum" "networkmanager-dmenu" "nwg-look" "pacseek" "pipewire" "qt6ct" "ranger" "sddm-config-editor" "systemd" "Thunar" "waybar" "wlogout" "wofi" "xsettingsd" "gtk-2.0" "gtk-3.0" "gtk-4.0" "starship" "swaync")

for folder in "${folders[@]}"; do
    folder_path="/home/<USER>/.config/$folder"
    backup_path="$backup_dir/$folder"
    if [ -d "$folder_path" ]; then
        backup "$folder_path" "$backup_path"
    else
        mkdir -p "$folder_path"
        backup "$folder_path" "$backup_path"
    fi
done

# Ensure the hyprland-dots directory exists
DOTFILES_DIR="$HOME/hyprland-dots"
mkdir -p "$DOTFILES_DIR"

# Repositories to clone
REPOS=(
    "https://github.com/RedBlizard/Hyprland-blizz.git"
    "https://github.com/RedBlizard/hypr-welcome.git"
    "https://github.com/RedBlizard/hypr-waybar.git"
)

# Set GIT_DISCOVERY_ACROSS_FILESYSTEM if needed
export GIT_DISCOVERY_ACROSS_FILESYSTEM=1

# Banner
show_message() {
    local message="$1"
    local color="$2"
    echo -e "${color}${message}${NC}"
}

# Function to handle git repository updates with conflict resolution
handle_git_update() {
    local repo_dir="$1"
    local repo_name="$2"
    
    cd "$repo_dir" || { show_message "Failed to change to directory $repo_dir." "$RED"; return 1; }
    
    # Configure git to handle divergent branches with fast-forward only
    git config pull.rebase false
    git config pull.ff only
    
    # Fetch the latest changes first
    if ! git fetch origin; then
        show_message "Failed to fetch from $repo_name repository." "$RED"
        show_message "Please check your internet connection and try again." "$RED"
        return 1
    fi
    
    # Check if we're on the main branch, if not switch to it
    current_branch=$(git branch --show-current)
    if [ "$current_branch" != "main" ]; then
        show_message "Switching to main branch..." "$BLUE"
        git checkout main || { 
            show_message "Failed to switch to main branch." "$RED"
            return 1
        }
    fi
    
    # Check the current state
    local_commit=$(git rev-parse HEAD 2>/dev/null)
    remote_commit=$(git rev-parse origin/main 2>/dev/null)
    
    if [ "$local_commit" = "$remote_commit" ]; then
        show_message "$repo_name is already up to date." "$GREEN"
        return 0
    fi
    
    # Check if local is ahead of remote (has unpushed commits)
    if git merge-base --is-ancestor origin/main HEAD && ! git merge-base --is-ancestor HEAD origin/main; then
        show_message "Local repository is ahead of remote. Resetting to remote state..." "$YELLOW"
        git reset --hard origin/main
        show_message "$repo_name reset to remote state." "$GREEN"
        return 0
    fi
    
    # Check if remote is ahead of local (normal update case)
    if git merge-base --is-ancestor HEAD origin/main; then
        show_message "Updating to latest remote changes..." "$BLUE"
        if git reset --hard origin/main; then
            show_message "$repo_name updated successfully." "$GREEN"
            return 0
        else
            show_message "Failed to update $repo_name." "$RED"
            return 1
        fi
    fi
    
    # If branches have diverged, reset to remote
    show_message "Branches have diverged. Resetting to remote state..." "$YELLOW"
    if git reset --hard origin/main; then
        show_message "$repo_name reset to remote state." "$GREEN"
        return 0
    else
        show_message "Failed to reset $repo_name." "$RED"
        return 1
    fi
}

show_message "░█─░█ █──█ █▀▀█ █▀▀█ █── █▀▀█ █▀▀▄ █▀▀▄ 　 ░█▀▀█ █── █── 　 ░▀░ █▀▀▄ 　 ▄▀▀▄ █▀▀▄ █▀▀" "$RED"
show_message "░█▀▀█ █▄▄█ █──█ █▄▄▀ █── █▄▄█ █──█ █──█ 　 ░█▄▄█ █── █── 　 ░█░ █──█ 　 █──█ █──█ █▀▀" "$RED"
show_message "░█─░█ ▄▄▄█ █▀▀▀ ▀─▀▀ ▀▀▀ ▀──▀ ▀──▀ ▀▀▀─ 　 ░█▄▄█ ▀▀▀ ▀▀▀ 　 ▄█▄ █▄▄▀ 　 ▀▀▀▀ ▀──▀ ▀▀▀" "$RED"
echo

# Check for updates in each repository BEFORE updating
show_message "Checking for available updates..." "$BLUE"
updates_available=false
repos_to_update=()

for repo in "${REPOS[@]}"; do
    repo_name=$(basename "$repo" .git)
    repo_dir="$DOTFILES_DIR/$repo_name"

    if [ -d "$repo_dir" ]; then
        cd "$repo_dir" || continue
        
        # Fetch the latest data from remote
        if git fetch origin main 2>/dev/null; then
            # Check if updates are available
            local_commit=$(git rev-parse HEAD 2>/dev/null)
            remote_commit=$(git rev-parse origin/main 2>/dev/null)
            
            if [ "$local_commit" != "$remote_commit" ]; then
                updates_available=true
                repos_to_update+=("$repo_name")
                show_message "Updates are available for $repo_name repository." "$BLUE"
            else
                show_message "$repo_name repository is up to date." "$GREEN"
            fi
        else
            show_message "Could not check updates for $repo_name (network issue?)." "$YELLOW"
        fi
    else
        # Repository doesn't exist, so we'll need to clone it
        updates_available=true
        repos_to_update+=("$repo_name")
        show_message "$repo_name repository not found - will be cloned." "$BLUE"
    fi
done

if [ "$updates_available" = true ]; then
    show_message "Updates are available for: ${repos_to_update[*]}" "$BLUE"
else
    show_message "All repositories are up to date." "$GREEN"
fi

# Clone or update the repositories
for repo in "${REPOS[@]}"; do
    repo_name=$(basename "$repo" .git)
    repo_dir="$DOTFILES_DIR/$repo_name"

    if [ ! -d "$repo_dir" ]; then
        show_message "Cloning $repo_name repository..." "$BLUE"
        if ! git clone "$repo" "$repo_dir"; then
            show_message "Failed to clone $repo_name repository." "$RED"
            show_message "Please check your internet connection and try again." "$RED"
            exit 1
        fi
        show_message "$repo_name cloned successfully." "$GREEN"
    else
        # Only update if updates are available for this specific repo
        if [[ " ${repos_to_update[*]} " =~ " ${repo_name} " ]]; then
            show_message "Updating $repo_name repository..." "$BLUE"
            if ! handle_git_update "$repo_dir" "$repo_name"; then
                show_message "Failed to update $repo_name repository." "$RED"
                show_message "You can continue with the current version or fix manually." "$YELLOW"
                
                read -r -p "Continue with current version? (Y/n): " continue_choice
                if [[ "$continue_choice" =~ ^[Nn]$ ]]; then
                    exit 1
                fi
            fi
        else
            show_message "$repo_name is already up to date, skipping update." "$GREEN"
        fi
    fi
done

read -rp "Do you want to update your dotfiles? (Enter 'Yy' for yes or 'Nn' for no): (Yy/Nn): " update_choice

if [[ "$update_choice" =~ ^[Yy]$ ]]; then
    # Copy dotfiles and directories from all repositories to home directory
    show_message "Updating dotfiles from all repositories..." "$BLUE"
    
    # Update from Hyprland-blizz (main configuration)
    source_dir="$HOME/hyprland-dots/Hyprland-blizz"
    if [ -d "$source_dir" ]; then
        show_message "Updating from Hyprland-blizz repository..." "$BLUE"
        
        # Copy main files and directories (excluding hidden files for now)
        if [ -d "$source_dir" ]; then
            show_message "Copying main directory contents..." "$BLUE"
            rsync -av --exclude='.*' "$source_dir"/ ~/ || { show_message "Failed to update main files from Hyprland-blizz." "$RED"; exit 1; }
        fi
        
        # Copy hidden directories individually with better error handling
        hidden_dirs=(".icons" ".Kvantum-themes" ".local" ".config")
        
        for dir in "${hidden_dirs[@]}"; do
            if [ -d "$source_dir/$dir" ]; then
                show_message "Copying $dir from Hyprland-blizz..." "$BLUE"
                rsync -av "$source_dir/$dir"/ ~/"$dir"/ || { show_message "Failed to update $dir from Hyprland-blizz." "$RED"; exit 1; }
            else
                show_message "Warning: $dir not found in Hyprland-blizz." "$BLUE"
            fi
        done
        
        # Copy Pictures directory
        if [ -d "$source_dir/Pictures" ]; then
            show_message "Copying Pictures from Hyprland-blizz..." "$BLUE"
            rsync -av "$source_dir/Pictures"/ ~/Pictures/ || { show_message "Failed to update Pictures from Hyprland-blizz." "$RED"; exit 1; }
        else
            show_message "Warning: Pictures directory not found in Hyprland-blizz." "$BLUE"
        fi
    else
        show_message "Warning: Hyprland-blizz source directory not found." "$RED"
    fi
    
    # Update from hypr-welcome repository
    source_dir="$HOME/hyprland-dots/hypr-welcome"
    if [ -d "$source_dir/.config" ]; then
        show_message "Updating from hypr-welcome repository..." "$BLUE"
        rsync -av "$source_dir/.config"/ ~/.config/ || { show_message "Failed to update config from hypr-welcome." "$RED"; exit 1; }
    else
        show_message "Warning: hypr-welcome config directory not found." "$YELLOW"
    fi
    
    # Update from hypr-waybar repository  
    source_dir="$HOME/hyprland-dots/hypr-waybar"
    if [ -d "$source_dir/.config" ]; then
        show_message "Updating from hypr-waybar repository..." "$BLUE"
        rsync -av "$source_dir/.config"/ ~/.config/ || { show_message "Failed to update config from hypr-waybar." "$RED"; exit 1; }
    else
        show_message "Warning: hypr-waybar config directory not found." "$YELLOW"
    fi
    
    show_message "Dotfiles update completed successfully from all repositories!" "$GREEN"

else
    show_message "No hyprland dotfiles update performed." "$BLUE"
    exit 0
fi

# Enable hypridle.service if not already enabled
show_message "Checking hypridle service..." "$BLUE"
if ! systemctl --user is-enabled hypridle.service >/dev/null 2>&1; then
    show_message "Enabling hypridle service..." "$BLUE"
    if systemctl --user enable --now hypridle.service; then
        show_message "hypridle service enabled successfully." "$GREEN"
    else
        show_message "Failed to enable hypridle service." "$RED"
    fi
else
    show_message "hypridle service is already enabled." "$GREEN"
fi

# Change to the home directory
cd "$HOME" || { echo 'Failed to change directory to home directory.'; exit 1; }

# Cleanup
show_message "Cleaning up unnecessary files..." "$BLUE"
cleanup_files=("$HOME/README.md" "$HOME/sddm-images" "$HOME/LICENSE" "$HOME/sddm.conf")

for file in "${cleanup_files[@]}"; do
    if [ -e "$file" ]; then
        rm -rf "$file" && show_message "Removed $file" "$BLUE"
    fi
done

# Change to the scripts directory
cd "$HOME/.config/hypr/scripts" || { echo "Failed to change to the scripts directory." >&2; exit 1; }

# Function to check if all required symlinks exist
check_symlinks() {
    local symlinks=("hypr-welcome" "hypr-eos-kill-yad-zombies" "hypr_check_updates")
    local all_exist=true
    
    for symlink in "${symlinks[@]}"; do
        if [ ! -L "/usr/bin/$symlink" ]; then
            all_exist=false
            break
        fi
    done
    
    if $all_exist; then
        echo "All required symlinks exist."
        return 0
    else
        echo "Some symlinks are missing."
        return 1
    fi
}

# Check if the symlinks exist
if ! check_symlinks; then
    show_message "Creating missing symlinks..." "$BLUE"
    
    # Change to the scripts directory
    cd "$HOME/.config/hypr/scripts" || { show_message "Failed to change to the scripts directory." "$RED"; exit 1; }

    # Array of scripts and their corresponding symlinks
    declare -A symlinks=(
        ["$HOME/.config/hypr/scripts/hypr-welcome"]="/usr/bin/hypr-welcome"
        ["$HOME/.config/hypr/scripts/hypr-eos-kill-yad-zombies"]="/usr/bin/hypr-eos-kill-yad-zombies"
        ["$HOME/.config/hypr/scripts/hypr_check_updates.sh"]="/usr/bin/hypr_check_updates"
    )

    # Create symlinks
    for script in "${!symlinks[@]}"; do
        symlink="${symlinks[$script]}"
        script_name=$(basename "$script")
        
        if [ -f "$script" ]; then
            if sudo ln -sf "$script" "$symlink"; then
                show_message "Created symlink for $script_name" "$GREEN"
            else
                show_message "Failed to create symlink for $script_name" "$RED"
            fi
        else
            show_message "Warning: Script $script not found" "$RED"
        fi
    done
else
    show_message "All required symlinks already exist." "$GREEN"
fi

# Change to the apps directory
cd "$HOME/.config/hypr/apps" || { echo "Failed to change to the apps directory." >&2; exit 1; }

# Function to check if the required symlink exists
check_settings_symlink() {
    local symlink="hypr-settings"
    
    if [ -L "/usr/bin/$symlink" ]; then
        echo "Settings symlink exists."
        return 0
    else
        echo "Settings symlink is missing."
        return 1
    fi
}

# Check if the settings symlink exists
if ! check_settings_symlink; then
    show_message "Creating missing settings symlink..." "$BLUE"
    
    # Path to your settings script
    settings_script="$HOME/.config/hypr/apps/AppRun"

    # Path to the symlink in /usr/bin
    symlink="/usr/bin/hypr-settings"

    # Check if the settings script exists
    if [ -f "$settings_script" ]; then
        # Create new symlink
        if sudo ln -sf "$settings_script" "$symlink"; then
            show_message "Created settings symlink successfully." "$GREEN"
        else
            show_message "Failed to create settings symlink." "$RED"
        fi
    else
        show_message "Warning: Settings script $settings_script not found." "$RED"
    fi
else
    show_message "Settings symlink already exists." "$GREEN"
fi

# Notify user about the end of the script
show_message "All-in-One Hyprland update process completed successfully!" "$GREEN"
show_message "All Hyprland repositories have been updated." "$GREEN"

# Send desktop notification if notify-send is available
if command -v notify-send >/dev/null 2>&1; then
    notify-send "Hyprland All-in-One Update Complete" "All repositories updated successfully. Enjoy your updated Hyprland experience!" --icon=dialog-information
else
    show_message "Note: notify-send not available for desktop notifications." "$BLUE"
fi

show_message "You may need to restart Hyprland to see all changes take effect." "$BLUE"
show_message "Log file saved to: $log_file" "$BLUE"

