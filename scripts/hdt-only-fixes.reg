Windows Registry Editor Version 5.00

; ═══════════════════════════════════════════════════════════════
; HDT-Only Registry Fixes (Battle.net Safe)
; These fixes target HDT overlay issues without breaking Battle.net
; ═══════════════════════════════════════════════════════════════

; Disable WPF Hardware Acceleration (CRITICAL for HDT overlay transparency)
[HKEY_CURRENT_USER\Software\Microsoft\.NETFramework]
"DisableHWAcceleration"=dword:00000001

[HKEY_LOCAL_MACHINE\Software\Microsoft\.NETFramework]
"DisableHWAcceleration"=dword:00000001

; HDT-specific application settings
[HKEY_CURRENT_USER\Software\HearthstoneDeckTracker]
"DisableHardwareAcceleration"=dword:00000001
"AlwaysOnTop"=dword:00000001
"OverlayOpacity"=dword:00000095

; .NET Framework 4.x specific settings for HDT
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\.NETFramework\v4.0.30319]
"SchUseStrongCrypto"=dword:00000001

[HKEY_CURRENT_USER\SOFTWARE\Microsoft\.NETFramework\v4.0.30319]
"SchUseStrongCrypto"=dword:00000001

; Windows Forms hardware acceleration disable (for HDT UI)
[HKEY_CURRENT_USER\Software\Microsoft\.NETFramework\Windows Forms]
"DisableHardwareAcceleration"=dword:00000001

; WPF rendering settings for better overlay compatibility
[HKEY_CURRENT_USER\Software\Microsoft\Avalon.Graphics]
"DisableHWAcceleration"=dword:00000001

; Font rendering improvements for overlay text
[HKEY_CURRENT_USER\Control Panel\Desktop]
"FontSmoothing"="2"
"FontSmoothingType"=dword:00000002

; Performance optimizations that don't break Battle.net
[HKEY_CURRENT_USER\Software\Wine\Direct3D]
"OffscreenRenderingMode"="backbuffer"

; Disable Windows Error Reporting (reduces interference)
[HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\Windows Error Reporting]
"Disabled"=dword:00000001
