#!/bin/bash

# Monitor script for random-wallpaper
SCRIPT_PATH="/home/<USER>/.config/hypr/scripts/random-wallpaper"
LOGFILE="$HOME/.local/share/random-wallpaper.log"
MONITOR_LOG="$HOME/.local/share/wallpaper-monitor.log"
LOCKFILE="/tmp/random-wallpaper.lock"

log_monitor() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] MONITOR: $1" >> "$MONITOR_LOG"
}

# Function to check if script is running
is_script_running() {
    if [[ -f "$LOCKFILE" ]]; then
        local pid=$(cat "$LOCKFILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # Running
        fi
    fi
    return 1  # Not running
}

# Function to get last log entry timestamp
get_last_activity() {
    if [[ -f "$LOGFILE" ]]; then
        tail -1 "$LOGFILE" | grep -o '\[.*\]' | tr -d '[]' || echo "never"
    else
        echo "never"
    fi
}

# Function to restart script if needed
restart_script() {
    log_monitor "Attempting to restart wallpaper script"
    
    # Kill any remaining processes
    pkill -f "random-wallpaper" 2>/dev/null
    rm -f "$LOCKFILE"
    
    # Wait a bit
    sleep 5
    
    # Start the script
    nohup "$SCRIPT_PATH" > /dev/null 2>&1 &
    
    sleep 2
    
    if is_script_running; then
        log_monitor "Script restarted successfully"
        return 0
    else
        log_monitor "Failed to restart script"
        return 1
    fi
}

# Main monitoring loop
log_monitor "Starting wallpaper monitor"

while true; do
    if is_script_running; then
        last_activity=$(get_last_activity)
        log_monitor "Script is running. Last activity: $last_activity"
        
        # Check if script has been inactive for too long (more than 15 minutes)
        if [[ "$last_activity" != "never" ]]; then
            last_timestamp=$(date -d "$last_activity" +%s 2>/dev/null || echo 0)
            current_timestamp=$(date +%s)
            inactive_time=$((current_timestamp - last_timestamp))
            
            if [[ $inactive_time -gt 900 ]]; then  # 15 minutes
                log_monitor "Script appears stuck (inactive for ${inactive_time} seconds). Restarting..."
                restart_script
            fi
        fi
    else
        log_monitor "Script is not running. Attempting restart..."
        restart_script
    fi
    
    # Check every 2 minutes
    sleep 120
done
