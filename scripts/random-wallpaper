#!/bin/bash

# Exit on any error
set -euo pipefail

# Directory for wallpapers
DIR="$HOME/Pictures/wallpapers-redblizard/"
CACHE_DIR="$HOME/.cache/swww"
LOCKFILE="/tmp/random-wallpaper.lock"
LOGFILE="$HOME/.local/share/random-wallpaper.log"

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOGFILE")"

# Function to cleanup on exit
cleanup() {
    local exit_code=$?
    log "Script exiting with code: $exit_code"
    if [[ $exit_code -ne 0 ]]; then
        log_error "<PERSON>rip<PERSON> crashed or exited unexpectedly"
        log_error "Last command: ${BASH_COMMAND:-unknown}"
        log_error "Line number: ${LINENO:-unknown}"
        # Log current system state for debugging
        log "System info at crash:"
        log "- swww status: $(swww query 2>&1 || echo "not running")"
        log "- Available memory: $(free -h | grep Mem: || echo "unknown")"
        log "- Disk space: $(df -h "$HOME" | tail -1 || echo "unknown")"
        log "- Process list: $(ps aux | grep -E "(swww|random-wallpaper)" | grep -v grep || echo "none")"
        log "- Environment: WAYLAND_DISPLAY=${WAYLAND_DISPLAY:-unset} XDG_SESSION_TYPE=${XDG_SESSION_TYPE:-unset}"
        
        # Create crash dump
        local crash_file="$HOME/.local/share/wallpaper-crash-$(date +%s).log"
        {
            echo "=== CRASH DUMP ==="
            echo "Date: $(date)"
            echo "Exit code: $exit_code"
            echo "Last command: ${BASH_COMMAND:-unknown}"
            echo "Script PID: $$"
            echo "Environment:"
            env | grep -E "(WAYLAND|XDG|HYPR)" || echo "No relevant env vars"
            echo "=== END CRASH DUMP ==="
        } > "$crash_file"
        log "Crash dump saved to: $crash_file"
    fi
    echo "Cleaning up..."
    rm -f "$LOCKFILE"
    exit $exit_code
}

# Set up signal handlers for graceful cleanup
trap cleanup SIGTERM SIGINT SIGQUIT

# Add error trap for debugging
trap 'log_error "Error on line $LINENO: Command was: $BASH_COMMAND"' ERR

# Function to log with timestamp
log() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    echo "$message" >> "$LOGFILE"
}

# Function to log errors
log_error() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo "$message" >&2
    echo "$message" >> "$LOGFILE"
}

# Function to rotate log file if it gets too large
rotate_log() {
    if [[ -f "$LOGFILE" ]] && [[ $(stat -f%z "$LOGFILE" 2>/dev/null || stat -c%s "$LOGFILE") -gt 1048576 ]]; then
        mv "$LOGFILE" "${LOGFILE}.old"
        log "Log file rotated"
    fi
}

# Check if already running (prevent multiple instances)
if [[ -f "$LOCKFILE" ]]; then
    if kill -0 "$(cat "$LOCKFILE")" 2>/dev/null; then
        log "Another instance is already running. Exiting."
        exit 1
    else
        log "Removing stale lockfile"
        rm -f "$LOCKFILE"
    fi
fi

# Create lockfile
echo $$ > "$LOCKFILE"

# Rotate log if needed
rotate_log

log "Starting random wallpaper changer (PID: $$)"
log "Script version: $(date '+%Y-%m-%d')"
log "Wallpaper directory: $DIR"
log "Cache directory: $CACHE_DIR"
log "Log file: $LOGFILE"

# Validate wallpaper directory
if [[ ! -d "$DIR" ]]; then
    log_error "Wallpaper directory '$DIR' does not exist!"
    cleanup
fi

# Count available wallpapers
wallpaper_count=$(find "$DIR" -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.gif" \) 2>/dev/null | wc -l)
log "Found $wallpaper_count wallpapers in directory"

if [[ $wallpaper_count -eq 0 ]]; then
    log_error "No wallpapers found in directory!"
    cleanup
fi

# Create cache directory if it doesn't exist
mkdir -p "$CACHE_DIR"

# Terminate any existing swaybg processes if running
if pidof swaybg >/dev/null 2>&1; then
    log "Terminating existing swaybg processes"
    pkill swaybg
fi

sleep 2

# Initialize swww if not running
if ! swww query >/dev/null 2>&1; then
    log "Initializing swww"
    if swww init; then
        log "swww initialized successfully"
        sleep 2  # Give swww time to start up
    else
        log_error "Failed to initialize swww"
        cleanup
    fi
else
    log "swww is already running"
fi

log "Starting wallpaper rotation loop"

# Heartbeat counter for monitoring
heartbeat_counter=0

# Loop to change wallpaper every 10 minutes with transition effects
while true; do
    # Increment heartbeat counter
    heartbeat_counter=$((heartbeat_counter + 1))
    
    # Log heartbeat every 10 iterations (for monitoring)
    if (( heartbeat_counter % 10 == 0 )); then
        log "Heartbeat: Loop iteration $heartbeat_counter, script healthy"
    fi

    # Check if we should exit
    if [[ ! -f "$LOCKFILE" ]] || [[ "$(cat "$LOCKFILE")" != "$$" ]]; then
        log "Lockfile missing or changed. Exiting."
        break
    fi

    # Find wallpapers with better error handling
    log "Searching for wallpapers in $DIR"
    
    # Use mapfile to safely populate array
    mapfile -t PICS < <(find "$DIR" -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.gif" \) 2>/dev/null)
    
    # Check if any wallpapers were found
    if [[ ${#PICS[@]} -eq 0 ]]; then
        log_error "No wallpapers found in $DIR. Waiting 60 seconds before retry..."
        sleep 60
        continue
    fi

    log "Found ${#PICS[@]} wallpapers available"

    # Sequential wallpaper selection
    INDEX_FILE="$CACHE_DIR/wallpaper_index"
    
    # Read current index, default to 0 if file doesn't exist
    if [[ -f "$INDEX_FILE" ]]; then
        CURRENT_INDEX=$(cat "$INDEX_FILE" 2>/dev/null || echo "0")
        # Validate index is a number
        if ! [[ "$CURRENT_INDEX" =~ ^[0-9]+$ ]]; then
            CURRENT_INDEX=0
        fi
    else
        CURRENT_INDEX=0
    fi
    
    # Ensure index is within bounds
    if [[ $CURRENT_INDEX -ge ${#PICS[@]} ]]; then
        CURRENT_INDEX=0
    fi
    
    # Select wallpaper at current index
    RANDOMPICS="${PICS[$CURRENT_INDEX]}"
    log "Selected wallpaper (index $CURRENT_INDEX): $(basename "$RANDOMPICS")"
    
    # Increment index for next time, wrapping around if necessary
    NEXT_INDEX=$(( (CURRENT_INDEX + 1) % ${#PICS[@]} ))
    echo "$NEXT_INDEX" > "$INDEX_FILE"
    log "Next wallpaper index will be: $NEXT_INDEX"

    # Verify the selected file exists and is readable
    if [[ ! -r "$RANDOMPICS" ]]; then
        log_error "Selected wallpaper is not readable: $RANDOMPICS"
        sleep 10
        continue
    fi

    # Log file details for debugging
    file_size=$(stat -f%z "$RANDOMPICS" 2>/dev/null || stat -c%s "$RANDOMPICS" 2>/dev/null || echo "unknown")
    log "Wallpaper details: size=${file_size} bytes"

    # Change wallpaper using swww with transition effects
    log "Attempting to change wallpaper..."
    if swww img "$RANDOMPICS" --transition-type wipe --transition-angle 30 --transition-step 30 --transition-fps 60 2>&1; then
        log "Successfully changed wallpaper"
        
        # Copy the selected wallpaper to cache directory
        if cp "$RANDOMPICS" "$CACHE_DIR/current_wallpaper.png" 2>&1; then
            log "Wallpaper cached successfully"
        else
            log_error "Failed to cache wallpaper"
        fi
    else
        log_error "Failed to change wallpaper with swww"
        # Check if swww is still running
        if ! swww query >/dev/null 2>&1; then
            log_error "swww appears to have crashed, attempting to restart..."
            swww init
            sleep 5
        fi
        sleep 30
        continue
    fi

    # Generate color scheme with pywal
    # wal -c
    # wal -i "$RANDOMPICS"

    # Uncomment the following line if you use Firefox with pywalfox extension
    # pywalfox updatecan 

    # Optionally clear the swww cache (comment out if not needed)
    # rm -rf $HOME/.cache/swww

    # Wait for 10 minutes before changing the wallpaper again
    log "Waiting 600 seconds before next wallpaper change"
    sleep 600
done

log "Exiting wallpaper changer"
cleanup
