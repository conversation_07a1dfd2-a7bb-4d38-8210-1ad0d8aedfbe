#!/bin/bash

# ═══════════════════════════════════════════════════════════════
# Battle.net Login Fix Script for Bottles/Wine
# Fixes authentication issues on Hyprland/Wayland
# ═══════════════════════════════════════════════════════════════

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Find correct bottle prefix
find_bottle_prefix() {
    local bottles_prefix="/home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A"

    if [[ -d "$bottles_prefix" ]]; then
        echo "$bottles_prefix"
    else
        print_error "Bottles prefix not found at $bottles_prefix"
        exit 1
    fi
}

# Apply Battle.net login fixes
apply_battlenet_fixes() {
    local prefix="$1"
    
    print_status "Applying Battle.net login fixes..."
    export WINEPREFIX="$prefix"
    
    # Install essential Windows components
    print_status "Installing required Windows components..."
    
    # Install Internet Explorer 8 (required for Battle.net authentication)
    winetricks -q ie8
    
    # Install Visual C++ redistributables
    winetricks -q vcrun2019 vcrun2017 vcrun2015 vcrun2013 vcrun2012 vcrun2010 vcrun2008 vcrun2005
    
    # Install .NET Framework 4.8
    winetricks -q dotnet48
    
    # Install essential fonts
    winetricks -q corefonts
    
    # Install Windows root certificates
    winetricks -q certs
    
    print_success "Windows components installed"
}

# Apply registry fixes for Battle.net
apply_registry_fixes() {
    local prefix="$1"
    export WINEPREFIX="$prefix"
    
    print_status "Applying registry fixes for Battle.net authentication..."
    
    # Set Windows version to Windows 10
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion" /v "CurrentVersion" /t REG_SZ /d "10.0" /f
    wine reg add "HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion" /v "CurrentBuild" /t REG_SZ /d "19041" /f
    
    # Enable TLS 1.2 and 1.3
    wine reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" /v "Enabled" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.2\Client" /v "DisabledByDefault" /t REG_DWORD /d 0 /f
    wine reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Client" /v "Enabled" /t REG_DWORD /d 1 /f
    wine reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\SecurityProviders\SCHANNEL\Protocols\TLS 1.3\Client" /v "DisabledByDefault" /t REG_DWORD /d 0 /f
    
    # Internet Explorer settings for authentication
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\Internet Explorer\Main" /v "Start Page" /t REG_SZ /d "about:blank" /f
    wine reg add "HKEY_CURRENT_USER\Software\Microsoft\Internet Explorer\Main" /v "Use FormSuggest" /t REG_SZ /d "no" /f
    
    # Disable IE Enhanced Security
    wine reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Active Setup\Installed Components\{A509B1A7-37EF-4b3f-8CFC-4F3A74704073}" /v "IsInstalled" /t REG_DWORD /d 0 /f
    wine reg add "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Active Setup\Installed Components\{A509B1A8-37EF-4b3f-8CFC-4F3A74704073}" /v "IsInstalled" /t REG_DWORD /d 0 /f
    
    # Battle.net specific fixes
    wine reg add "HKEY_CURRENT_USER\Software\Blizzard Entertainment\Battle.net" /v "AllowBetaClient" /t REG_DWORD /d 0 /f
    wine reg add "HKEY_CURRENT_USER\Software\Blizzard Entertainment\Battle.net" /v "DisableHardwareAcceleration" /t REG_DWORD /d 1 /f
    
    print_success "Registry fixes applied"
}

# Create Battle.net launcher script
create_launcher_script() {
    local prefix="$1"
    
    cat > "$HOME/.local/bin/battlenet-launcher" << EOF
#!/bin/bash

# Battle.net Launcher Script with Login Fixes
export WINEPREFIX="$prefix"
export WINEDLLOVERRIDES="winemenubuilder.exe=d"

# Disable hardware acceleration
export MESA_GL_VERSION_OVERRIDE=3.3
export MESA_GLSL_VERSION_OVERRIDE=330

# Set Wine to Windows 10 mode
export WINEARCH=win64

# Launch Battle.net with specific parameters
cd "\$WINEPREFIX/drive_c/Program Files (x86)/Battle.net"
wine "Battle.net Launcher.exe" --exec="launch WTCG" --productcode=WTCG
EOF

    chmod +x "$HOME/.local/bin/battlenet-launcher"
    print_success "Battle.net launcher script created at ~/.local/bin/battlenet-launcher"
}

# Alternative login method
show_alternative_methods() {
    print_status "=== ALTERNATIVE LOGIN METHODS ==="
    echo
    echo "If Battle.net still won't log in, try these methods:"
    echo
    echo "Method 1: Use Web Browser Login"
    echo "1. Open Battle.net in Wine"
    echo "2. When login screen appears, click 'Use a web browser to log in'"
    echo "3. This will open your system browser for authentication"
    echo
    echo "Method 2: Offline Mode"
    echo "1. Disconnect from internet"
    echo "2. Launch Battle.net (it will go to offline mode)"
    echo "3. Reconnect internet"
    echo "4. Battle.net should remember your credentials"
    echo
    echo "Method 3: Clear Battle.net Cache"
    echo "1. Close Battle.net completely"
    echo "2. Delete: \$WINEPREFIX/drive_c/ProgramData/Battle.net/Cache"
    echo "3. Delete: \$WINEPREFIX/drive_c/users/\$USER/AppData/Local/Battle.net"
    echo "4. Restart Battle.net"
    echo
}

# Main execution
main() {
    echo "═══════════════════════════════════════════════════════════════"
    echo "Battle.net Login Fix Script for Bottles/Wine"
    echo "═══════════════════════════════════════════════════════════════"
    echo
    
    # Find correct bottle prefix
    bottle_prefix=$(find_bottle_prefix)
    print_success "Found bottle: $bottle_prefix"
    
    # Check if winetricks is installed
    if ! command -v winetricks &> /dev/null; then
        print_error "winetricks is not installed. Please install it first:"
        print_error "sudo pacman -S winetricks"
        exit 1
    fi
    
    # Apply fixes
    apply_registry_fixes "$bottle_prefix"

    # Ask user if they want to install components (this takes time)
    echo
    read -p "Do you want to install Windows components? This may take 10-15 minutes. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        apply_battlenet_fixes "$bottle_prefix"
    else
        print_warning "Skipping Windows components installation"
        print_warning "You can run this script again with 'install' argument to install them later"
    fi

    # Create launcher script
    mkdir -p "$HOME/.local/bin"
    create_launcher_script "$bottle_prefix"
    
    # Show alternative methods
    show_alternative_methods
    
    echo
    print_success "Battle.net login fixes applied!"
    echo
    echo "Next steps:"
    echo "1. Close Battle.net completely if it's running"
    echo "2. Launch Battle.net from Bottles again"
    echo "3. Try logging in - it should work now"
    echo "4. If still stuck, try the alternative methods shown above"
    echo
}

# Handle arguments
if [[ $# -eq 1 && "$1" == "install" ]]; then
    bottle_prefix=$(find_bottle_prefix)
    apply_battlenet_fixes "$bottle_prefix"
elif [[ $# -eq 1 && "$1" == "registry-only" ]]; then
    bottle_prefix=$(find_bottle_prefix)
    apply_registry_fixes "$bottle_prefix"
else
    main
fi
