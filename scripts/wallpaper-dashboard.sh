#!/bin/bash

# Wallpaper Script Dashboard
clear
echo "🖼️  RANDOM WALLPAPER DASHBOARD"
echo "================================"
echo "$(date)"
echo

# Function to show section
show_section() {
    echo "📋 $1"
    echo "$(printf '=%.0s' {1..40})"
}

# Status check
show_section "CURRENT STATUS"
bash /home/<USER>/.config/hypr/scripts/wallpaper-status.sh
echo

# Recent activity
show_section "RECENT ACTIVITY (Last 5 log entries)"
if [[ -f "$HOME/.local/share/random-wallpaper.log" ]]; then
    tail -5 "$HOME/.local/share/random-wallpaper.log"
else
    echo "No log file found"
fi
echo

# Error summary
show_section "ERROR SUMMARY"
if [[ -f "$HOME/.local/share/random-wallpaper.log" ]]; then
    error_count=$(grep -c "ERROR:" "$HOME/.local/share/random-wallpaper.log" 2>/dev/null || echo 0)
    crash_count=$(grep -c "crashed or exited unexpectedly" "$HOME/.local/share/random-wallpaper.log" 2>/dev/null || echo 0)
    
    echo "Total errors: $error_count"
    echo "Total crashes: $crash_count"
    
    if [[ $error_count -gt 0 ]]; then
        echo "Last error:"
        grep "ERROR:" "$HOME/.local/share/random-wallpaper.log" | tail -1
    fi
else
    echo "No log file to analyze"
fi
echo

# Systemd service status
show_section "SYSTEMD SERVICE"
if systemctl --user is-enabled random-wallpaper.service >/dev/null 2>&1; then
    echo "Service status: $(systemctl --user is-active random-wallpaper.service)"
    echo "Service enabled: $(systemctl --user is-enabled random-wallpaper.service)"
else
    echo "❌ Systemd service not set up"
    echo "   To enable: systemctl --user enable random-wallpaper.service"
    echo "   To start:  systemctl --user start random-wallpaper.service"
fi
echo

# Available commands
show_section "QUICK COMMANDS"
echo "🔧 Management:"
echo "   View this dashboard: /home/<USER>/.config/hypr/scripts/wallpaper-dashboard.sh"
echo "   Check status:        /home/<USER>/.config/hypr/scripts/wallpaper-status.sh"
echo "   Analyze logs:        /home/<USER>/.config/hypr/scripts/analyze-wallpaper-logs.sh"
echo "   Start monitor:       /home/<USER>/.config/hypr/scripts/monitor-wallpaper.sh &"
echo
echo "📊 Systemd (recommended):"
echo "   Enable service:      systemctl --user enable random-wallpaper.service"
echo "   Start service:       systemctl --user start random-wallpaper.service"
echo "   Stop service:        systemctl --user stop random-wallpaper.service"
echo "   View logs:           journalctl --user -u random-wallpaper.service -f"
echo
echo "🛠️  Manual:"
echo "   Start script:        /home/<USER>/.config/hypr/scripts/random-wallpaper &"
echo "   Stop script:         pkill -f random-wallpaper"
echo "   View live logs:      tail -f $HOME/.local/share/random-wallpaper.log"
echo
echo "🔄 To refresh this dashboard, run this script again"
