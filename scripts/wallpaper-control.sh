#!/bin/bash

# Wallpaper Control Script
# Control sequential wallpaper cycling

CACHE_DIR="$HOME/.cache/swww"
INDEX_FILE="$CACHE_DIR/wallpaper_index"
DIR="$HOME/Pictures/Wallpapers/Anime"

show_help() {
    echo "Wallpaper Control Script"
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  next       - Skip to next wallpaper immediately"
    echo "  prev       - Go back to previous wallpaper"
    echo "  reset      - Reset to first wallpaper (index 0)"
    echo "  status     - Show current wallpaper index and total count"
    echo "  goto <n>   - Go to specific wallpaper by index"
    echo "  list       - List all wallpapers with indices"
    echo "  help       - Show this help message"
}

get_wallpapers() {
    mapfile -t PICS < <(find "$DIR" -type f \( -name "*.jpg" -o -name "*.png" -o -name "*.gif" \) 2>/dev/null | sort)
}

get_current_index() {
    if [[ -f "$INDEX_FILE" ]]; then
        CURRENT_INDEX=$(cat "$INDEX_FILE" 2>/dev/null || echo "0")
        if ! [[ "$CURRENT_INDEX" =~ ^[0-9]+$ ]]; then
            CURRENT_INDEX=0
        fi
    else
        CURRENT_INDEX=0
    fi
}

set_wallpaper() {
    local index=$1
    get_wallpapers
    
    if [[ $index -ge ${#PICS[@]} ]] || [[ $index -lt 0 ]]; then
        echo "Error: Index $index is out of range (0-$((${#PICS[@]}-1)))"
        return 1
    fi
    
    local wallpaper="${PICS[$index]}"
    echo "Setting wallpaper (index $index): $(basename "$wallpaper")"
    
    if swww img "$wallpaper" --transition-type wipe --transition-angle 30 --transition-step 30 --transition-fps 60; then
        echo "$index" > "$INDEX_FILE"
        echo "Successfully set wallpaper"
        
        # Copy to cache
        mkdir -p "$CACHE_DIR"
        cp "$wallpaper" "$CACHE_DIR/current_wallpaper.png" 2>/dev/null
    else
        echo "Error: Failed to set wallpaper"
        return 1
    fi
}

case "${1:-help}" in
    "next")
        get_current_index
        get_wallpapers
        NEXT_INDEX=$(( (CURRENT_INDEX + 1) % ${#PICS[@]} ))
        set_wallpaper $NEXT_INDEX
        ;;
    "prev")
        get_current_index
        get_wallpapers
        PREV_INDEX=$(( (CURRENT_INDEX - 1 + ${#PICS[@]}) % ${#PICS[@]} ))
        set_wallpaper $PREV_INDEX
        ;;
    "reset")
        set_wallpaper 0
        ;;
    "status")
        get_current_index
        get_wallpapers
        if [[ ${#PICS[@]} -gt 0 ]]; then
            echo "Current wallpaper index: $CURRENT_INDEX"
            echo "Total wallpapers: ${#PICS[@]}"
            echo "Current wallpaper: $(basename "${PICS[$CURRENT_INDEX]}")"
            echo "Next wallpaper: $(basename "${PICS[$(( (CURRENT_INDEX + 1) % ${#PICS[@]} ))]})"
        else
            echo "No wallpapers found in $DIR"
        fi
        ;;
    "goto")
        if [[ -z "$2" ]] || ! [[ "$2" =~ ^[0-9]+$ ]]; then
            echo "Error: Please provide a valid index number"
            echo "Usage: $0 goto <index>"
            exit 1
        fi
        set_wallpaper "$2"
        ;;
    "list")
        get_wallpapers
        echo "Available wallpapers:"
        for i in "${!PICS[@]}"; do
            echo "  [$i] $(basename "${PICS[$i]}")"
        done
        ;;
    "help"|*)
        show_help
        ;;
esac
