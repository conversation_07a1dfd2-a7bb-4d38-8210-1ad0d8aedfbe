#!/bin/bash

# Log analyzer for random-wallpaper crashes
LOGFILE="$HOME/.local/share/random-wallpaper.log"
MONITOR_LOG="$HOME/.local/share/wallpaper-monitor.log"

echo "=== Random Wallpaper Crash Analyzer ==="
echo "Date: $(date)"
echo

# Check if log files exist
if [[ ! -f "$LOGFILE" ]]; then
    echo "❌ Main log file not found: $LOGFILE"
    exit 1
fi

echo "📊 Log Analysis Summary:"
echo "========================"

# Basic statistics
total_lines=$(wc -l < "$LOGFILE")
error_count=$(grep -c "ERROR:" "$LOGFILE" || echo 0)
crash_count=$(grep -c "Script crashed or exited unexpectedly" "$LOGFILE" || echo 0)
restart_count=$(grep -c "Starting random wallpaper changer" "$LOGFILE" || echo 0)

echo "📈 Statistics:"
echo "  - Total log entries: $total_lines"
echo "  - Error count: $error_count"
echo "  - Crash count: $crash_count"
echo "  - Restart count: $restart_count"
echo

# Show recent errors
echo "🔥 Recent Errors (last 10):"
echo "=============================="
grep "ERROR:" "$LOGFILE" | tail -10 || echo "No errors found"
echo

# Show crashes with context
echo "💥 Crash Details:"
echo "=================="
if [[ $crash_count -gt 0 ]]; then
    grep -A 5 -B 2 "Script crashed or exited unexpectedly" "$LOGFILE" | tail -20
else
    echo "No crashes detected in logs"
fi
echo

# Show last activity
echo "🕒 Recent Activity (last 20 lines):"
echo "===================================="
tail -20 "$LOGFILE"
echo

# Check system resources when crashes occurred
echo "🖥️  System State During Crashes:"
echo "================================="
grep -A 10 "System info at crash:" "$LOGFILE" | tail -20 || echo "No crash system info found"
echo

# Analyze patterns
echo "🔍 Pattern Analysis:"
echo "===================="

# Check for common error patterns
swww_errors=$(grep -c "Failed to change wallpaper with swww" "$LOGFILE" || echo 0)
init_errors=$(grep -c "Failed to initialize swww" "$LOGFILE" || echo 0)
file_errors=$(grep -c "not readable" "$LOGFILE" || echo 0)

echo "  - swww command failures: $swww_errors"
echo "  - swww initialization failures: $init_errors"
echo "  - File read errors: $file_errors"

# Check for memory/disk issues
if grep -q "Available memory:" "$LOGFILE"; then
    echo "  - Memory info logged (check for low memory)"
fi

if grep -q "Disk space:" "$LOGFILE"; then
    echo "  - Disk space info logged (check for full disk)"
fi

# Monitor log analysis
if [[ -f "$MONITOR_LOG" ]]; then
    echo
    echo "🔧 Monitor Log Summary:"
    echo "======================="
    monitor_restarts=$(grep -c "Script restarted successfully" "$MONITOR_LOG" || echo 0)
    monitor_failures=$(grep -c "Failed to restart script" "$MONITOR_LOG" || echo 0)
    stuck_detections=$(grep -c "appears stuck" "$MONITOR_LOG" || echo 0)
    
    echo "  - Monitor restarts: $monitor_restarts"
    echo "  - Monitor restart failures: $monitor_failures"
    echo "  - Stuck script detections: $stuck_detections"
    
    echo
    echo "Recent monitor activity:"
    tail -10 "$MONITOR_LOG" || echo "No monitor log entries"
fi

echo
echo "🎯 Recommendations:"
echo "==================="

if [[ $swww_errors -gt 5 ]]; then
    echo "  ⚠️  High number of swww failures - check swww installation and Wayland session"
fi

if [[ $crash_count -gt 3 ]]; then
    echo "  ⚠️  Multiple crashes detected - consider running with more verbose logging"
fi

if [[ $file_errors -gt 0 ]]; then
    echo "  ⚠️  File access errors - check wallpaper directory permissions"
fi

echo "  💡 To monitor in real-time: tail -f '$LOGFILE'"
echo "  💡 To start monitor: '/home/<USER>/.config/hypr/scripts/monitor-wallpaper.sh &'"
echo "  💡 To check if running: 'ps aux | grep random-wallpaper'"
