#!/bin/bash

# Audio output switcher script for Hyprland
# Usage: ./audio-switch.sh [output_name]

# Function to send notification
notify() {
    local message="$1"
    local icon="$2"
    notify-send -t 2000 -i "$icon" "Audio Output" "$message"
}

# Function to get current default sink
get_current_sink() {
    pactl get-default-sink
}

# Function to set audio output
set_audio_output() {
    local sink_name="$1"
    local display_name="$2"
    
    # Check if sink exists
    if pactl list short sinks | grep -q "$sink_name"; then
        # Set as default sink
        pactl set-default-sink "$sink_name"
        
        # Move all currently playing streams to the new sink
        pactl list short sink-inputs | while read stream; do
            streamId=$(echo $stream | cut '-d ' -f1)
            pactl move-sink-input "$streamId" "$sink_name" 2>/dev/null
        done
        
        notify "Switched to: $display_name" "audio-speakers"
        echo "Audio output switched to: $display_name ($sink_name)"
    else
        notify "Error: $display_name not found" "dialog-error"
        echo "Error: Sink $sink_name not found"
        exit 1
    fi
}

# Function to show current output
show_current() {
    current_sink=$(get_current_sink)
    case "$current_sink" in
        "alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__SPDIF__sink")
            display_name="USB SPDIF"
            ;;
        "alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Headphones__sink")
            display_name="USB Headphones"
            ;;
        "alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Speaker__sink")
            display_name="USB 5.1 Speakers"
            ;;
        "alsa_output.usb-SteelSeries_Arctis_Nova_Pro_Wireless-00.analog-stereo")
            display_name="Arctis Nova Pro Wireless"
            ;;
        *)
            display_name="Unknown ($current_sink)"
            ;;
    esac
    notify "Current output: $display_name" "audio-speakers"
    echo "Current audio output: $display_name ($current_sink)"
}

# Main script logic
case "$1" in
    "arctis"|"arctis-pro"|"steelseries")
        set_audio_output "alsa_output.usb-SteelSeries_Arctis_Nova_Pro_Wireless-00.analog-stereo" "Arctis Nova Pro Wireless"
        ;;
    "spdif"|"usb-spdif")
        set_audio_output "alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__SPDIF__sink" "USB SPDIF"
        ;;
    "headphones"|"usb-headphones")
        set_audio_output "alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Headphones__sink" "USB Headphones"
        ;;
    "speakers"|"5.1"|"usb-speakers")
        set_audio_output "alsa_output.usb-Generic_USB_Audio-00.HiFi_5_1__Speaker__sink" "USB 5.1 Speakers"
        ;;
    "current"|"show")
        show_current
        ;;
    "list")
        echo "Available audio outputs:"
        echo "  arctis       - Arctis Nova Pro Wireless"
        echo "  spdif        - USB SPDIF"
        echo "  headphones   - USB Headphones" 
        echo "  speakers     - USB 5.1 Speakers"
        echo ""
        echo "Commands:"
        echo "  current      - Show current output"
        echo "  list         - Show this help"
        ;;
    *)
        echo "Usage: $0 [arctis|spdif|headphones|speakers|current|list]"
        echo "Run '$0 list' for more details"
        exit 1
        ;;
esac
