#█▀▄▀█ █▀█ █▄ █ █ ▀█▀ █▀█ █▀█
#█ ▀ █ █▄█ █ ▀█ █  █  █▄█ █▀▄
#Monitor Definitions

# see the wiki https://wiki.hyprland.org/Configuring/Monitors/

# QEMU is Virtua-1 1920x1080@60,auto,1 

# Monitor Definitions
monitor = eDP-1,1920x1080@60.000,0x0,1
monitor = Other-2,3840x2160@60.000,0x0,1
monitor = Other-3,1280x720@60.000,0x0,1
monitor = Other-1,2560x1440@60.000,0x0,1
monitor = Other-4,1600x900@60.000,0x0,1
monitor = Other-5,1366x768@60.000,0x0,1
monitor = DP-3, 3440x1440@175.000,1920x0,1
monitor = DP-2,1920x1080@60.000,5360x489,1
monitor = DP-1,1920x1080@60.000,0x0,1
monitor = HDMI-A-1,1920x1080@60.000,0x489,1
monitor = Virtual-1,1920x1080@60,auto,1


#monitor = ,preferred,auto,1
#monitor = ,highrr,auto,1
#monitor = ,highres,auto,1

#dev Rudy-in uses these settings
#monitor = ,preferred,auto,0.85
#monitor = ,highres,auto,0.85
#env = GDK_SCALE,0.85

#Transform list:
#normal (no transforms) -> 0
#90 degrees -> 1
#180 degrees -> 2
#270 degrees -> 3
#flipped -> 4
#flipped + 90 degrees -> 5
#flipped + 180 degrees -> 6
#flipped + 270 degrees -> 7
