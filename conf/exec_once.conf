 
#█▀▀ ▀▄▀ █▀▀ █▀▀ ▄▄ █▀█ █▄░█ █▀▀ █▀▀
#██▄ █░█ █▄▄ ██▄ ░░ █▄█ █░▀█ █▄▄ ██▄


# script checks all the hyprland sourced conf files and auto reloads after config change.
    exec-once = fish ~/.config/hypr/scripts/watch-hyprland-config.sh

# reloads the systemd-deamon need for the watch-hyprland-config.sh script
    exec-once = ~/.config/hypr/scripts/restart-hyprland-config.sh

# script blocks the eos-welcome app from running the first time but can still be run manualy
    exec-once = ~/.config/hypr/scripts/disable-eos-welcome.sh

# Execute scripts specific to Hyprland configuration
    exec-once = ~/.config/hypr/scripts/reset_xdgportals.sh
    exec-once = ~/.config/hypr/scripts/monitors.sh
   #exec-once = ~/.config/hypr/scripts/monitor_workspaces_configurator.sh
    exec-once = ~/.config/hypr/scripts/hypr_check_updates.sh
    exec-once = ~/.config/hypr/scripts/welcome.sh
    exec-once = ~/.config/hypr/apps/install.sh
    exec-once = ~/.config/hypr/scripts/chmod_scripts.sh &
    exec-once = ~/.config/hypr/scripts/nvidia_gpu_check.sh 
    exec-once = ~/.config/hypr/scripts/kvm-qemu.sh 
    exec-once = ~/.config/hypr/scripts/unlock-pacman.sh 
    exec-once = ~/.config/waybar/scripts/update_waybar_network-device.sh 
    exec-once = swww-daemon &
    exec-once = ~/.config/hypr/scripts/random-wallpaper 
   #exec-once = ~/.config/hypr/scripts/changeWallpaper2 & # Uncomment this line if needed
    exec-once = ~/.config/waybar/scripts/hypridle_toggle.sh &
    exec-once = ~/.config/waybar/scripts/firewall-applet.sh &
   #exec-once = ~/.config/hypr/scripts/dunst.sh
    exec-once = swaync &

# Additional Hyprland-specific commands
    exec-once = dbus-update-activation-environment --systemd WAYLAND_DISPLAY XDG_CURRENT_DESKTOP 
    exec-once = systemctl --user import-environment WAYLAND_DISPLAY XDG_CURRENT_DESKTOP 
    exec-once = /usr/lib/polkit-gnome/polkit-gnome-authentication-agent-1
    exec-once = echo us > /tmp/kb_layout 
    exec-once = hyprctl setcursor Qogir-dark 24
    exec-once = systemctl --user start pipewire
    exec-once = wl-paste --type text --watch cliphist store 
    exec-once = wl-paste --type image --watch cliphist store
    exec-once = hypridle
   #exec-once = wlsunset -S 8:09 -s 17:40 & # Uncomment this line if needed
   #exec-once = blueman-applet &  # Uncomment this line if needed
    exec-once = udev-block-notify &
    exec-once = udiskie &

# My stuff
    exec-once = [workspace 6 silent] vesktop
    exec-once = [workspace 1 silent] opera
    exec-once = [workspace 5 silent] steam -silent
    exec-once = insync start
    exec-once = copyq --start-server 
    exec-once = xrandr --output DP-3 --primary
    exec-once = bash -c 'openrgb --noautoconnect --startminimized -p setup & sleep 10 && killall openrgb'

