
#█▀▀ █▄ █ █ █
#██▄ █ ▀█ ▀▄▀

# Set your environment variables

# Hyprland Environment Variables
    env = HYPRLAND_TRACE=0 # Enables more verbose logging.
    env = HYPRLAND_NO_RT=1 # Disables realtime priority setting by Hyprland.
    env = HYPRLAND_NO_SD_NOTIFY=1 # If systemd, disables the sd_notify calls.
    env = HYPRLAND_NO_SD_VARS=1 # Disables management of variables in systemd and dbus activation environments.
    env = HYPRLAND_CONFIG # Specifies where you want your Hyprland configuration.
    
# Aquamarine Environment Variables    
    env = AQ_TRACE=0 # Enables more verbose logging.
    env = AQ_DRM_DEVICES= # Set an explicit list of DRM devices (GPUs) to use. It’s a colon-separated list of paths, with the first being the primary. E.g. /dev/dri/card1:/dev/dri/card0
    env = AQ_MGPU_NO_EXPLICIT=1 # Disables explicit syncing on mgpu buffers
    env = AQ_NO_MODIFIERS=1 # Disables modifiers for DRM buffers

# XDG Specifications
    env = XDG_CURRENT_DESKTOP=Hyprland
    env = XDG_SESSION_TYPE=wayland
    env = XDG_SESSION_DESKTOP=Hyprland
    env = XDG_MENU_PREFIX,arch-    

# Toolkit Backend Variables
    env = GDK_BACKEND,wayland,x11,*     
    env = CLUTTER_BACKEND=wayland
    env = WAYLAND_DISPLAY=wayland-0,wayland-1
    env = DISPLAY=:1

# Theming and UI specific environment variables
    env = XCURSOR_THEME=Qogir-dark
    env = XCURSOR_SIZE=24
   #env = GTK_THEME,Colloid-Dark-Catppuccin
    env = GTK_USE_PORTAL,1
   
# Qt specific environment variables   
    env = QT_QPA_PLATFORM,wayland;xcb
    env = QT_QPA_PLATFORMTHEME,qt6ct
    env = QT_WAYLAND_DISABLE_WINDOWDECORATION=1
    env = QT_AUTO_SCREEN_SCALE_FACTOR=1
  
# Application specific environment variables
    env = SDL_VIDEODRIVER=wayland
    env = _JAVA_AWT_WM_NONREPARENTING=1
    env = MOZ_DISABLE_RDD_SANDBOX=1
    env = MOZ_ENABLE_WAYLAND=1

# Platform specific environment variables
    env = OZONE_PLATFORM=wayland,x11
    env = WLR_NO_HARDWARE_CURSORS=1 # comment the rule with (#) when you have nvidia
    env = WLR_RENDERER_ALLOW_SOFTWARE=1

 # █▄░█ █░█ █ █▀▄ █ ▄▀█
 # █░▀█ ▀▄▀ █ █▄▀ █ █▀█

#----------------
# NVIDIA Specific 
#----------------

#----------------------------------------------------------
# if you have Nvidia uncomment the rules below remove the # 
#----------------------------------------------------------

   env = LIBVA_DRIVER_NAME,nvidia
   env = XDG_SESSION_TYPE,wayland
   env = GBM_BACKEND,nvidia-drm
   env = __GLX_VENDOR_LIBRARY_NAME,nvidia
   env = WLR_NO_HARDWARE_CURSORS,1

# NVIDIA Anti-Flicker and Sync Settings
   env = __GL_VRR_ALLOWED,0
   env = WLR_DRM_NO_ATOMIC,1
   env = __GL_MaxFramesAllowed,1
   env = __GL_SYNC_TO_VBLANK,1

# XWayland and Wine Optimization
   env = XWAYLAND_NO_GLAMOR,1
   env = WLR_XWAYLAND,1

# ═══════════════════════════════════════════════════════════════
# HDT (Hearthstone Deck Tracker) Specific Environment Variables
# ═══════════════════════════════════════════════════════════════

# Wine/HDT Specific Settings
   env = WINEDLLOVERRIDES,d3d11=disabled;dxgi=disabled
   env = WINEPREFIX,~/.local/share/bottles/bottles/Hearthstone/drive_c
   env = WINE_LARGE_ADDRESS_AWARE,1

# WPF Hardware Acceleration Disable (fixes black overlay background)
   env = DOTNET_SYSTEM_WINDOWS_MEDIA_DISABLEHARDWAREACCELERATION,1
   env = DOTNET_SYSTEM_WINDOWS_FORMS_DISABLEHARDWAREACCELERATION,1

# Additional NVIDIA optimizations for overlay rendering
   env = __GL_THREADED_OPTIMIZATIONS,1
   env = __GL_SHADER_DISK_CACHE,1
   env = __GL_SHADER_DISK_CACHE_PATH,/tmp
   env = __GL_ALLOW_UNOFFICIAL_PROTOCOL,1

# Force software rendering for overlay transparency
   env = WLR_RENDERER,vulkan
   env = WLR_DRM_DEVICES,/dev/dri/card0

# HDT Overlay specific fixes
   env = QT_X11_NO_MITSHM,1
   env = _JAVA_AWT_WM_NONREPARENTING,1
