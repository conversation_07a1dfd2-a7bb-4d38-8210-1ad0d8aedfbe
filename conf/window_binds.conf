
 # █░█░█ █ █▄░█ █▀▄ █▀█ █░█░█   █▀▄▀█ ▄▀█ █▄░█ ▄▀█ █▀▀ █▀▄▀█ █▀▀ █▄░█ ▀█▀
 # ▀▄▀▄▀ █ █░▀█ █▄▀ █▄█ ▀▄▀▄▀   █░▀░█ █▀█ █░▀█ █▀█ █▄█ █░▀░█ ██▄ █░▀█ ░█░

    bind = $mainMod, C, killactive,
    bind = $mainMod ALT, F, fullscreen,
    bind = $mainMod ALT, G, togglesplit, #
    bind = $mainMod, Space, togglefloating,
    bind = $mainMod ALT, P, pseudo, # dwindle

# to switch between windows in a floating workspace
    bind = SUPER,Tab,cyclenext,          # change focus to another window
    bind = SUPER,Tab,bringactivetotop,   # bring it to the top

# █ █ █ █▀█ █▀█ █▄▀ █▀ █▀█ ▄▀█ █▀▀ █▀▀   █▀▄▀█ █▀█ █▀▄ █▀▀
# ▀▄▀▄▀ █▄█ █▀▄ █ █ ▄█ █▀▀ █▀█ █▄▄ ██▄   █ ▀ █ █▄█ █▄▀ ██▄

# Change Workspace Mode
    bind = $mainMod SHIFT, Space, workspaceopt, allfloat
    bind = $mainMod SHIFT, Space, exec, $notifycmd 'Toggled All Float Mode'
    bind = $mainMod SHIFT, P, workspaceopt, allpseudo
    bind = $mainMod SHIFT, P, exec, $notifycmd 'Toggled All Pseudo Mode'

    bind = $mainMod, Tab, cyclenext,
    bind = $mainMod, Tab, bringactivetotop,

# █▀▀ █▀█ █▀▀ █ █ █▀
# █▀  █▄█ █▄▄ █▄█ ▄█

    bind = $mainMod, left, movefocus, l
    bind = $mainMod, right, movefocus, r
    bind = $mainMod, up, movefocus, u
    bind = $mainMod, down, movefocus, d

# █▀▄▀█ █▀█ █ █ █▀▀   █ █ █ █ █▄ █ █▀▄ █▀█ █   █
# █ ▀ █ █▄█ ▀▄▀ ██▄   ▀▄▀▄▀ █ █ ▀█ █▄▀ █▄█ ▀▄▀▄▀

# Move (vim style)
    bind = $mainMod CTRL, left, movewindow, l
    bind = $mainMod CTRL, right, movewindow, r
    bind = $mainMod CTRL, up, movewindow, u
    bind = $mainMod CTRL, down, movewindow, d

# Move focus with mainMod + arrow keys
    bind = $mainMod, H, movefocus, l
    bind = $mainMod, J, movefocus, r
    bind = $mainMod, K, movefocus, u
    bind = $mainMod, L, movefocus, d

# █▀█ █▀▀ █▀ █ ▀█ █▀▀
# █▀▄ ██▄ ▄█ █ █▄ ██▄

# Resize (vim style)
    binde = $mainMod SHIFT, H, resizeactive,-50 0
    binde = $mainMod SHIFT, L, resizeactive,50 0
    binde = $mainMod SHIFT, K, resizeactive,0 -50
    binde = $mainMod SHIFT, J, resizeactive,0 50

    binde = $mainMod SHIFT, left, resizeactive,-50 0
    binde = $mainMod SHIFT, right, resizeactive,50 0
    binde = $mainMod SHIFT, up, resizeactive,0 -50
    binde = $mainMod SHIFT, down, resizeactive,0 50

# ▀█▀ ▄▀█ █▄▄ █▄▄ █▀▀ █▀▄
#  █  █▀█ █▄█ █▄█ ██▄ █▄▀

    bind = $mainMod, g, togglegroup
    bind = $mainMod, tab, changegroupactive
    bind = $mainMod, G, exec, $notifycmd 'Toggled Group Mode'

 # █▀ █▀█ █▀▀ █▀▀ █ ▄▀█ █
 # ▄█ █▀▀ ██▄ █▄▄ █ █▀█ █▄▄
    
    bind = $mainMod, S, togglespecialworkspace
    bind = $mainMod SHIFT, E, movetoworkspace, special
    bind = $mainMod, S, exec, $notifycmd 'Toggled Special Workspace'
    bind = $mainMod, a, exec, hyprctl dispatch centerwindow
# Special workspace
    bind = $mainMod SHIFT, U, movetoworkspace, special
    bind = $mainMod, U, togglespecialworkspace,

# █▀ █ █ █ █ ▀█▀ █▀▀ █ █
# ▄█ ▀▄▀▄▀ █  █  █▄▄ █▀█

# Switch workspaces with mainMod + [0-9]
    bind = $mainMod, 1, workspace, 1
    bind = $mainMod, 2, workspace, 2
    bind = $mainMod, 3, workspace, 3
    bind = $mainMod, 4, workspace, 4
    bind = $mainMod, 5, workspace, 5
    bind = $mainMod, 6, workspace, 6
    bind = $mainMod, 7, workspace, 7
    bind = $mainMod, 8, workspace, 8
    bind = $mainMod, 9, workspace, 9
    bind = $mainMod, 0, workspace, 10


    bind = $mainMod ALT, up, workspace, e+1
    bind = $mainMod ALT, down, workspace, e-1

# █▀▄▀█ █▀█ █ █ █▀▀
# █ ▀ █ █▄█ ▀▄▀ ██▄

# Move active window and follow to workspace
    bind = $mainMod CTRL, 1, movetoworkspace, 1
    bind = $mainMod CTRL, 2, movetoworkspace, 2
    bind = $mainMod CTRL, 3, movetoworkspace, 3
    bind = $mainMod CTRL, 4, movetoworkspace, 4
    bind = $mainMod CTRL, 5, movetoworkspace, 5
    bind = $mainMod CTRL, 6, movetoworkspace, 6
    bind = $mainMod CTRL, 7, movetoworkspace, 7
    bind = $mainMod CTRL, 8, movetoworkspace, 8
    bind = $mainMod CTRL, 9, movetoworkspace, 9
    bind = $mainMod CTRL, 0, movetoworkspace, 10
    bind = $mainMod CTRL, bracketleft, movetoworkspace, -1
    bind = $mainMod CTRL, bracketright, movetoworkspace, +1

# Move active window to a workspace with mainMod + SHIFT + [0-9]
    bind = $mainMod SHIFT, 1, movetoworkspacesilent, 1
    bind = $mainMod SHIFT, 2, movetoworkspacesilent, 2
    bind = $mainMod SHIFT, 3, movetoworkspacesilent, 3
    bind = $mainMod SHIFT, 4, movetoworkspacesilent, 4
    bind = $mainMod SHIFT, 5, movetoworkspacesilent, 5
    bind = $mainMod SHIFT, 6, movetoworkspacesilent, 6
    bind = $mainMod SHIFT, 7, movetoworkspacesilent, 7
    bind = $mainMod SHIFT, 8, movetoworkspacesilent, 8
    bind = $mainMod SHIFT, 9, movetoworkspacesilent, 9
    bind = $mainMod SHIFT, 0, movetoworkspacesilent, 10
    bind = $mainMod SHIFT, bracketleft, movetoworkspacesilent, -1
    bind = $mainMod SHIFT, bracketright, movetoworkspacesilent, +1


# █▀▄▀█ █▀█ █ █ █▀ █▀▀   █▄▄ █ █▄ █ █▀▄ █ █▄ █ █▀▀
# █ ▀ █ █▄█ █▄█ ▄█ ██▄   █▄█ █ █ ▀█ █▄▀ █ █ ▀█ █▄█
   
# Scroll through existing workspaces with mainMod + scroll
    bind = $mainMod, mouse_down, workspace, e+1
    bind = $mainMod, mouse_up, workspace, e-1
    bind = CTRL, right, workspace, e+1
    bind = CTRL, left, workspace, e-1

# Move/resize windows with mainMod + LMB/RMB and dragging
    bindm = $mainMod, mouse:272, movewindow
    bindm = $mainMod, mouse:273, resizewindow
