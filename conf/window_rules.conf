
 # █ █ █ █ █▄ █ █▀▄ █▀█ █ █ █   █▀█ █ █ █   █▀▀ █▀
 # ▀▄▀▄▀ █ █ ▀█ █▄▀ █▄█ ▀▄▀▄▀   █▀▄ █▄█ █▄▄ ██▄ ▄█
      
# Opacity 
     windowrulev2 = opacity 0.90 0.90,class:^(Brave-browser)$
     windowrulev2 = opacity 0.80 0.80,class:^(geany)$
     windowrulev2 = opacity 0.80 0.80,class:^(thunar)$
     windowrulev2 = opacity 0.80 0.80,class:^(file-roller)$
     windowrulev2 = opacity 0.80 0.80,class:^(nwg-look)$
     windowrulev2 = opacity 0.80 0.80,class:^(qt6ct)$
     windowrulev2 = opacity 0.80 0.70,class:^(pavucontrol)$
     windowrulev2 = opacity 0.80 0.80,class:^(yad)$
     windowrulev2 = opacity 0.80 0.80,class:^(nvim)$
     
# Fullscreen
    windowrule = fullscreen, title:^(wlogout)$
    windowrule = float, title:^(wlogout)$     
     
# Windows tiled
     windowrulev2 = tile, class:tile
     windowrulev2 = tile,class:^(libreoffice)$
     windowrulev2 = tile,class:^(azote)$
     windowrulev2 = tile,class:^(Brave-browser)$
     windowrulev2 = tile,class:^(chromium.desktop)$
     windowrulev2 = tile,class:^(firedragon)$
     windowrulev2 = tile,class:^(firefox)$ 
     windowrulev2 = tile,class:^(kitty)$ 
     windowrulev2 = tile,class:^(timeshift-gtk)$ 
     windowrulev2 = tile,class:^(sddm-conf)$ 
     
# Windows float
     windowrulev2 = float, class:floating     
     windowrulev2 = float,class:^(Alacritty)$
     windowrulev2 = float,class:^(kitty_floats)$ 
     windowrulev2 = float,class:^(pavucontrol-qt)$
     windowrulev2 = float,class:^(nm-connection-editor)$
     windowrulev2 = float,class:^(yad)$  

# Floating Windows Positions
     windowrule = float, title:^(branchdialog)$
     windowrule = float, title:^(confirmreset)$  
     windowrule = float, title:^(error)$
     windowrule = float, title:^(file-roller)$
     windowrule = float, title:^(Media viewer)$
     windowrule = float, title:^(notification)$
     windowrule = float, title:^(splash)$
     windowrule = float, title:^(xfce4-terminal)$
         
     windowrule = float, center, title:^(blueman-manager)$
     windowrule = float, center, title:^(confirm)$
     windowrule = float, center, title:^(download)$
     windowrule = float, center, title:^(dialog)$
     windowrule = float, center, title:^(file_progress)$
     windowrule = float, center, title:^(galculator)$
     windowrule = float, center, title:^(konsole)$
     windowrule = float, center, title:^(nm-connection-editor)$
     windowrule = float, center, title:^(Open File)$    
     windowrule = float, center, title:^(pavucontrol-qt)$
     windowrule = float, center, title:^(Rofi)$
     windowrule = float, center, title:^(Wofi)$
     windowrule = float, center, title:^(yad)$     

# None animated floating windows
     windowrule = float, animation none, title:^(Rofi)$     
     windowrule = float, animation none, title:^(Wofi)$      
     
# Window size   
    windowrulev2 = size 1888 1006, title:^(Alacritty)$ 
    windowrulev2 = size 800 600,class:^(download)$
    windowrulev2 = size 800 600,title:^(Open File)$
    windowrulev2 = size 800 600,title:^(Save File)$
    windowrulev2 = size 800 600,title:^(Volume Control)$
    windowrulev2 = size 800 600,class:^(kitty_floats)$
    
# Special Rules
    windowrulev2 = float,class:^(brave)$,title:^(Save File)$
    windowrulev2 = float,class:^(brave)$,title:^(Open File)$
    windowrulev2 = float,title:^(Picture-in-Picture)$
    windowrulev2 = float,class:^(xdg-desktop-portal-gtk)$
    windowrulev2 = float,class:^(/usr/lib/polkit-gnome/polkit-gnome-authentication-agent-1)$
    windowrule = float, class:(kitty), title:(kitty_floats)
    windowrule = float, move cursor -50% -50%, title:^(kitty_floats)$  

# Idleinhibit rules
    windowrulev2 = idleinhibit focus,class:^(mpv)$
    windowrulev2 = idleinhibit focus,class:^(obs)$
    windowrulev2 = idleinhibit fullscreen,class:^(opera)$
    windowrulev2 = idleinhibit focus,class:^(opera)$

# xwaylandvideobridge rules
    windowrulev2 = opacity 0.0 override 0.0 override,class:^(xwaylandvideobridge)$
    windowrulev2 = noanim,class:^(xwaylandvideobridge)$
    windowrulev2 = nofocus,class:^(xwaylandvideobridge)$
    windowrulev2 = noinitialfocus,class:^(xwaylandvideobridge)$     

# Move
    windowrulev2 = move 0 0,title:^(flameshot) 
    windowrulev2 = move 0 5%,class:^(Alacritty)$
    windowrulev2 = move 1% 5%,class:^(Alacritty)$
  
# special example rule for opening nvim on a empty workspace only works with hyprland-git version testing !!!
# workspace = 4, on-created-empty:[tile] kitty -e nvim & 

windowrulev2 = workspace 6 silent,class:^(vesktop)$
windowrulev2 = workspace 5 silent,class:^(steam)$
windowrulev2 = idleinhibit fullscreen, class:.*

windowrulev2 = workspace 5,class:^(starcitizen\.exe)$
windowrulev2 = fullscreen,class:^(starcitizen\.exe)$

# ═══════════════════════════════════════════════════════════════
# HDT (Hearthstone Deck Tracker) Overlay Rules
# ═══════════════════════════════════════════════════════════════

# HDT Main Application Window
windowrulev2 = float,class:^(HearthstoneDeckTracker\.exe)$
windowrulev2 = float,title:^(Hearthstone Deck Tracker)$
windowrulev2 = size 800 600,class:^(HearthstoneDeckTracker\.exe)$

# HDT Overlay Windows - Critical for proper overlay functionality
windowrulev2 = float,class:^(HDTOverlay)$
windowrulev2 = float,title:^(HDT Overlay)$
windowrulev2 = float,title:^(Player Deck)$
windowrulev2 = float,title:^(Opponent Deck)$
windowrulev2 = float,title:^(Secrets)$
windowrulev2 = float,title:^(Turn Timer)$

# Force overlay windows to stay on top and be pinned
windowrulev2 = pin,class:^(HDTOverlay)$
windowrulev2 = pin,title:^(HDT Overlay)$
windowrulev2 = pin,title:^(Player Deck)$
windowrulev2 = pin,title:^(Opponent Deck)$
windowrulev2 = pin,title:^(Secrets)$
windowrulev2 = pin,title:^(Turn Timer)$

# Disable animations for overlay windows to prevent flickering
windowrulev2 = noanim,class:^(HDTOverlay)$
windowrulev2 = noanim,title:^(HDT Overlay)$
windowrulev2 = noanim,title:^(Player Deck)$
windowrulev2 = noanim,title:^(Opponent Deck)$
windowrulev2 = noanim,title:^(Secrets)$
windowrulev2 = noanim,title:^(Turn Timer)$

# Prevent overlay windows from being focused (click-through behavior)
windowrulev2 = nofocus,class:^(HDTOverlay)$
windowrulev2 = nofocus,title:^(HDT Overlay)$
windowrulev2 = nofocus,title:^(Player Deck)$
windowrulev2 = nofocus,title:^(Opponent Deck)$
windowrulev2 = nofocus,title:^(Secrets)$
windowrulev2 = nofocus,title:^(Turn Timer)$

# Ensure overlay windows don't get initial focus
windowrulev2 = noinitialfocus,class:^(HDTOverlay)$
windowrulev2 = noinitialfocus,title:^(HDT Overlay)$
windowrulev2 = noinitialfocus,title:^(Player Deck)$
windowrulev2 = noinitialfocus,title:^(Opponent Deck)$
windowrulev2 = noinitialfocus,title:^(Secrets)$
windowrulev2 = noinitialfocus,title:^(Turn Timer)$

# Set opacity for overlay windows (adjust as needed)
windowrulev2 = opacity 0.95 0.95,class:^(HDTOverlay)$
windowrulev2 = opacity 0.95 0.95,title:^(HDT Overlay)$
windowrulev2 = opacity 0.95 0.95,title:^(Player Deck)$
windowrulev2 = opacity 0.95 0.95,title:^(Opponent Deck)$
windowrulev2 = opacity 0.95 0.95,title:^(Secrets)$
windowrulev2 = opacity 0.95 0.95,title:^(Turn Timer)$

# Hearthstone Game Window Rules
windowrulev2 = float,class:^(Hearthstone\.exe)$
windowrulev2 = float,title:^(Hearthstone)$
windowrulev2 = size 1920 1080,class:^(Hearthstone\.exe)$
windowrulev2 = center,class:^(Hearthstone\.exe)$

# Wine/Bottles specific rules for HDT
windowrulev2 = float,class:^(wine)$,title:^(Hearthstone Deck Tracker)$
windowrulev2 = float,class:^(bottles)$,title:^(Hearthstone Deck Tracker)$
windowrulev2 = pin,class:^(wine)$,title:^(HDT Overlay)$
windowrulev2 = pin,class:^(bottles)$,title:^(HDT Overlay)$
