# Battle.net DLL Error Fix Summary

## 🚨 **Problem Identified**
The "A required DLL could not be found" error in Battle.net was caused by overly aggressive DLL overrides in our initial HDT overlay fix:

- `d3d11=disabled` - Battle.net needs D3D11 for its UI rendering
- `dxgi=disabled` - Battle.net needs DXGI for graphics interface
- `dwmapi=disabled` - Battle.net needs Desktop Window Manager API

## ✅ **Solution Applied**

### **Removed Problematic Settings:**
- ❌ Removed `WINEDLLOVERRIDES=d3d11=disabled;dxgi=disabled` from environment variables
- ❌ Removed D3D11, DXGI, and DWMAPI DLL overrides from Wine registry
- ❌ Removed overly restrictive graphics settings

### **Kept Essential HDT Fixes:**
- ✅ WPF hardware acceleration disabled (fixes black overlay background)
- ✅ .NET Framework hardware acceleration disabled
- ✅ Windows Forms hardware acceleration disabled
- ✅ HDT-specific application settings
- ✅ Hyprland window rules for overlay behavior

### **Added Battle.net Compatibility:**
- ✅ Windows 10 compatibility mode
- ✅ TLS 1.2 support for authentication
- ✅ Internet Explorer settings for web login
- ✅ Battle.net specific non-breaking settings

## 🎯 **Current Status**

### **Battle.net:**
- ✅ Should launch without DLL errors
- ✅ Authentication should work (use web browser login if needed)
- ✅ Can install and launch games normally

### **HDT Overlay:**
- ✅ Transparency fixes maintained (no black background)
- ✅ Overlay stability preserved
- ✅ Hyprland window rules still active
- ✅ Anti-flicker settings still in place

## 🔧 **Technical Details**

### **Why the Original Approach Failed:**
1. **D3D11/DXGI are essential** - Modern applications like Battle.net require these for GPU-accelerated UI rendering
2. **DLL overrides are global** - Disabling them affects all applications in the Wine prefix
3. **Battle.net is more sensitive** - It performs strict DLL validation during startup

### **Why the New Approach Works:**
1. **Targeted fixes** - Only disable hardware acceleration in .NET/WPF (affects HDT only)
2. **Application-specific** - HDT settings don't interfere with Battle.net
3. **Compatibility maintained** - Both applications can coexist

## 📋 **Registry Changes Made**

### **Removed (Problematic):**
```
HKEY_CURRENT_USER\Software\Wine\DllOverrides
├── d3d11 = "disabled" ❌ REMOVED
├── dxgi = "disabled" ❌ REMOVED
└── dwmapi = "disabled" ❌ REMOVED
```

### **Added/Kept (Safe):**
```
HKEY_CURRENT_USER\Software\Microsoft\.NETFramework
└── DisableHWAcceleration = 1 ✅ KEPT

HKEY_CURRENT_USER\Software\HearthstoneDeckTracker
├── DisableHardwareAcceleration = 1 ✅ ADDED
├── AlwaysOnTop = 1 ✅ ADDED
└── OverlayOpacity = 95 ✅ ADDED

HKEY_LOCAL_MACHINE\Software\Microsoft\Windows NT\CurrentVersion
└── CurrentVersion = "10.0" ✅ ADDED
```

## 🚀 **Testing Instructions**

### **Test Battle.net:**
1. Launch Battle.net from Bottles
2. Should start without "DLL could not be found" error
3. Login should work (use web browser option if needed)
4. Should be able to install/launch Hearthstone

### **Test HDT Overlay:**
1. Launch Hearthstone in **windowed mode** (critical!)
2. Launch HDT
3. Configure overlay settings (opacity 95%, always on top)
4. Start a game - overlay should be transparent and stable

## 🔄 **If Issues Persist**

### **Battle.net Still Has DLL Errors:**
```bash
# Check what DLLs are actually missing
WINEDEBUG=+loaddll wine "Battle.net Launcher.exe"
```

### **HDT Overlay Still Has Black Background:**
```bash
# Verify .NET hardware acceleration is disabled
wine reg query "HKEY_CURRENT_USER\Software\Microsoft\.NETFramework" /v "DisableHWAcceleration"
```

### **Need to Start Over:**
```bash
# Run the compatible fix script again
cd ~/.config/hypr
./scripts/battlenet-hdt-compatible-fix.sh
```

## 📈 **Success Metrics**

When everything is working correctly:
- ✅ Battle.net launches without errors
- ✅ Battle.net can authenticate and show game library
- ✅ Hearthstone launches and runs smoothly
- ✅ HDT overlay appears transparently over Hearthstone
- ✅ No flickering or black background on overlay
- ✅ Overlay stays on top during gameplay

## 🎉 **Conclusion**

The fix successfully separates Battle.net compatibility from HDT overlay functionality by:
1. **Removing global DLL overrides** that broke Battle.net
2. **Applying targeted .NET/WPF fixes** that only affect HDT
3. **Maintaining Hyprland window rules** for proper overlay behavior
4. **Adding compatibility settings** that benefit both applications

Both applications should now work together without conflicts.
