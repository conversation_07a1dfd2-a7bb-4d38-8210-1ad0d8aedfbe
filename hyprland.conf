
 #██╗░░██╗██╗░░░██╗██████╗░██████╗░██╗░░░░░░█████╗░███╗░░██╗██████╗░░░░░█████╗░░█████╗░███╗░░██╗███████╗
 #██║░░██║╚██╗░██╔╝██╔══██╗██╔══██╗██║░░░░░██╔══██╗████╗░██║██╔══██╗░░░██╔══██╗██╔══██╗████╗░██║██╔════╝
 #███████║░╚████╔╝░██████╔╝██████╔╝██║░░░░░███████║██╔██╗██║██║░░██║░░░██║░░╚═╝██║░░██║██╔██╗██║█████╗░░
 #██╔══██║░░╚██╔╝░░██╔═══╝░██╔══██╗██║░░░░░██╔══██║██║╚████║██║░░██║░░░██║░░██╗██║░░██║██║╚████║██╔══╝░░
 #██║░░██║░░░██║░░░██║░░░░░██║░░██║███████╗██║░░██║██║░╚███║██████╔╝██╗╚█████╔╝╚█████╔╝██║░╚███║██║░░░░░
 #╚═╝░░╚═╝░░░╚═╝░░░╚═╝░░░░░╚═╝░░╚═╝╚══════╝╚═╝░░╚═╝╚═╝░░╚══╝╚═════╝░╚═╝░╚════╝░░╚════╝░╚═╝░░╚══╝╚═╝░░░░░
 #                            By RedBlizard April 10 2024

# █▀ █▀█ █░█ █▀█ █▀▀ █▀▀
# ▄█ █▄█ █▄█ █▀▄ █▄▄ ██▄

# Source a file (multi-file configs)
    source = ~/.config/hypr/conf/debug.conf
    source = ~/.config/hypr/conf/exec_once.conf
    source = ~/.config/hypr/conf/env_var.conf
    source = ~/.config/hypr/conf/monitor.conf
    source = ~/.config/hypr/conf/workspaces.conf
    source = ~/.config/hypr/conf/key_binds.conf
    source = ~/.config/hypr/conf/window_binds.conf
    source = ~/.config/hypr/conf/window_rules.conf

# ▀▄▀ █░█░█ ▄▀█ █▄█ █░░ ▄▀█ █▄░█ █▀▄
# █░█ ▀▄▀▄▀ █▀█ ░█░ █▄▄ █▀█ █░▀█ █▄▀

xwayland {
    force_zero_scaling = false # change if needed       
    use_nearest_neighbor = true
    enabled = true
}

#█ █▄ █ █▀█ █░█ ▀█▀
#█ █ ▀█ █▀▀ █▄█  █

input {
    kb_layout = ch
    kb_variant = fr
    kb_model =
    kb_options =
    kb_rules =    
    numlock_by_default = true

#█▀▄▀█ █▀█ █░█ █▀ █▀▀ ░░▄▀ ▀█▀ █▀█ █░█ █▀▀ █░█
#█░▀░█ █▄█ █▄█ ▄█ ██▄ ▄▀░░ ░█░ █▄█ █▄█ █▄▄ █▀█

    follow_mouse = 1
    sensitivity = 0 # -1.0 - 1.0, 0 means no modification.
    float_switch_override_focus = true

touchpad {
    natural_scroll = true
    tap-to-click = true
    drag_lock = true
    disable_while_typing = true
} 

}
 # █▀▀ █▀▀ █▄ █ █▀▀ █▀█ ▄▀█ █
 # █▄█ ██▄ █ ▀█ ██▄ █▀▄ █▀█ █▄▄
 
general {
    gaps_in=3
    gaps_out=8
    border_size=2  # Adjust this value as needed
    no_border_on_floating = false # enable border on float window
    layout = dwindle # master|dwindle
   #col.active_border = 0xff89ddff 0xff7dcfff 0xff7aa2f7 270deg # Set active border colour (gradient)
    col.inactive_border = 0xff414868 # Inactive gray
    col.active_border = rgb(8839EF) rgb(7CB6F5) rgb(FD807E) 45deg # Set active border colour (gradient)
   #col.inactive_border = rgb(d8dee9)
    resize_on_border = true

}

# █▀▄ █▀▀ █▀▀ █▀█ █▀█ ▄▀█ ▀█▀ █ █▀█ █▄ █
# █▄▀ ██▄ █▄▄ █▄█ █▀▄ █▀█  █  █ █▄█ █ ▀█
 
decoration {
  
# █▀█ █▀█ █ █ █▄ █ █▀▄   █▀▀ █▀█ █▀█ █▄ █ █▀▀ █▀█
# █▀▄ █▄█ █▄█ █ ▀█ █▄▀   █▄▄ █▄█ █▀▄ █ ▀█ ██▄ █▀▄

    rounding = 5

# █▀█ █▀█ ▄▀█ █▀▀ █ ▀█▀ █▄█
# █▄█ █▀▀ █▀█ █▄▄ █  █   █ 

    active_opacity = 1.0
    inactive_opacity = 1.0
    fullscreen_opacity = 1.0

# █▄▄ █   █ █ █▀█
# █▄█ █▄▄ █▄█ █▀▄

 blur {
    enabled = true
    size = 6
    passes = 2
    new_optimizations = on
    ignore_opacity = true
    xray = true  
   
# █▀█ █ █ █   █▀▀ █▀
# █▀▄ █▄█ █▄▄ ██▄ ▄█ 
  
   #layerrule = blur,waybar
   #layerrule = ignorezero, waybar
   #layerrule = blur,lockscreen
    layerrule = blur, wofi
    layerrule = blur, wlogout
    layerrule = ignorezero, wofi
    layerrule = xray 1, wlogout
    layerrule = xray 1, wofi
   #layerrule = blur,gtk-layer-shell # blurs wlogout
}
          
# █▀ █ █ ▄▀█ █▀▄ █▀█ █ █ █
# ▄█ █▀█ █▀█ █▄▀ █▄█ ▀▄▀▄▀

shadow {
    enabled = true
    range = 30
    render_power = 3
    color = 0x66000000
}
    dim_inactive = false
}  
 # ▄▀█ █▄ █ █ █▀▄▀█ ▄▀█ ▀█▀ █ █▀█ █▄ █
 # █▀█ █ ▀█ █ █ ▀ █ █▀█  █  █ █▄█ █ ▀█


animations {
    enabled = yes
    
# █▄▄ █▀▀ ▀█ █ █▀▀ █▀█   █▀▀ █ █ █▀█ █ █ █▀▀
# █▄█ ██▄ █▄ █ ██▄ █▀▄   █▄▄ █▄█ █▀▄ ▀▄▀ ██▄   
    
    bezier = liner, 1, 1, 1, 1
    bezier = md3_standard, 0.2, 0, 0, 1
    bezier = md3_decel, 0.05, 0.7, 0.1, 1
    bezier = md3_accel, 0.3, 0, 0.8, 0.15
    bezier = overshot, 0.05, 0.9, 0.1, 1.1
    bezier = crazyshot, 0.1, 1.5, 0.76, 0.92 
    bezier = hyprnostretch, 0.05, 0.9, 0.1, 1.0
    bezier = menu_decel, 0.1, 1, 0, 1
    bezier = menu_accel, 0.38, 0.04, 1, 0.07
    bezier = easeInOutCirc, 0.85, 0, 0.15, 1
    bezier = easeOutCirc, 0, 0.55, 0.45, 1
    bezier = easeOutExpo, 0.16, 1, 0.3, 1
    bezier = softAcDecel, 0.26, 0.26, 0.15, 1
    bezier = md2, 0.4, 0, 0.2, 1 # use with .2s duration
  
    animation = windows, 1, 3, md3_decel, popin 60%
    animation = windowsIn, 1, 3, md3_decel, popin 60%
    animation = windowsOut, 1, 3, md3_accel, popin 60%
    animation = border, 1, 10, default
    animation = fade, 1, 3, md3_decel
    animation = layersIn, 1, 3, menu_decel, slide
    animation = layersOut, 1, 1.6, menu_accel
    animation = fadeLayersIn, 1, 2, menu_decel
    animation = fadeLayersOut, 1, 4.5, menu_accel
    animation = border, 1, 1, liner
    animation = borderangle, 1, 75, liner, loop
    animation = fade, 1, 10, default
    animation = workspaces, 1, 6, default,slide
    animation = workspaces, 1, 7, menu_decel, slide
    animation = specialWorkspace, 1, 3, md3_decel, slidevert
}
   
 # █   ▄▀█ █▄█ █▀█ █ █ ▀█▀ █▀
 # █▄▄ █▀█  █  █▄█ █▄█  █  ▄█
   
dwindle {
    pseudotile = true
    force_split = 0 
    special_scale_factor = 0.8
    split_width_multiplier = 1.0 
    use_active_for_splits = true
   #pseudotile = yes 
   #preserve_split = true
    smart_resizing = true
   #smart_split = true
}

master {
   #new_is_master = true
    special_scale_factor = 0.8
}

# █▀▄▀█ █ █▀ █▀▀
# █ ▀ █ █ ▄█ █▄▄

misc {
    disable_hyprland_logo = true
    disable_splash_rendering = true
    animate_manual_resizes = true
    vfr = true
    vrr = 0
    enable_swallow = true
    swallow_regex = ^(kitty)$
    focus_on_activate = true
    allow_session_lock_restore = true
    new_window_takes_over_fullscreen = 2
    animate_manual_resizes = true
    close_special_on_empty = true
    mouse_move_enables_dpms = true
    key_press_enables_dpms = true
    disable_autoreload = true
    mouse_move_focuses_monitor = true
    always_follow_on_dnd = true
    disable_hyprland_qtutils_check = true
}  
binds {
    workspace_back_and_forth = true
    allow_workspace_cycles = true
    pass_mouse_when_bound = true
}
