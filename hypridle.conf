
#  ██╗  ██╗██╗   ██╗██████╗ ██╗██████╗ ██╗     ███████╗
#  ██║  ██║╚██╗ ██╔╝██╔══██╗██║██╔══██╗██║     ██╔════╝
#  ███████║ ╚████╔╝ ██████╔╝██║██║  ██║██║     █████╗  
#  ██╔══██║  ╚██╔╝  ██╔═══╝ ██║██║  ██║██║     ██╔══╝  
#  ██║  ██║   ██║   ██║     ██║██████╔╝███████╗███████╗
#  ╚═╝  ╚═╝   ╚═╝   ╚═╝     ╚═╝╚═════╝ ╚══════╝╚══════╝
#                                                      
# general {
#     ignore_dbus_inhibit = false
# }

general {
    lock_cmd = pidof hyprlock || hyprlock       # avoid starting multiple hyprlock instances.
    before_sleep_cmd = loginctl lock-session    # lock before suspend.
    after_sleep_cmd = hyprctl dispatch dpms on  # to avoid having to press a key twice to turn on the display.
}

# Screen_brightness
listener {
	timeout = 110
	on-timeout = brightnessctl s 20%
	on-resume = brightnessctl s 100%
}

# Screenlock
listener {
    # HYPRLOCK TIMEOUT
    timeout = 300
    # HYPRLOCK ONTIMEOUT
    on-timeout = loginctl lock-session
}

# dpms
listener {
    # DPMS TIMEOUT
    timeout = 660
    # DPMS ONTIMEOUT
    on-timeout = hyprctl dispatch dpms off
    # DPMS ONRESUME
    on-resume = hyprctl dispatch dpms on
}

# Suspend
listener {
    # SUSPEND TIMEOUT
    timeout = 1800
    #SUSPEND ONTIMEOUT
    on-timeout = systemctl suspend
}


