# Battle.net Login Fix Guide for Bottles/Wine

## 🚨 **Quick Fixes (Try These First)**

### **Fix 1: Clear Battle.net Cache**
```bash
# Close Battle.net completely first, then:
rm -rf /home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A/drive_c/ProgramData/Battle.net/Cache
rm -rf /home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A/drive_c/users/*/AppData/Local/Battle.net
rm -rf /home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A/drive_c/users/*/AppData/Roaming/Battle.net
```

### **Fix 2: Use Web Browser Login**
1. Launch Battle.net from Bottles
2. When the login screen appears, look for **"Use a web browser to log in"** or **"Log in with browser"**
3. Click it - this will open your system browser
4. Log in through your browser
5. Return to Battle.net - it should be logged in

### **Fix 3: Offline Mode Trick**
1. Disconnect from internet (unplug ethernet or disable WiFi)
2. Launch Battle.net - it will show offline mode
3. Reconnect to internet
4. Battle.net should automatically reconnect with saved credentials

## 🔧 **Advanced Fixes (If Quick Fixes Don't Work)**

### **Fix 4: Install Internet Explorer 8**
```bash
# This is required for Battle.net authentication
WINEPREFIX=/home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A winetricks ie8
```

### **Fix 5: Install Visual C++ Redistributables**
```bash
WINEPREFIX=/home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A winetricks vcrun2019
```

### **Fix 6: Reset Wine Prefix (Nuclear Option)**
⚠️ **Warning: This will delete all your games and require reinstalling everything**
```bash
# Backup your saves first!
rm -rf /home/<USER>/.var/app/com.usebottles.bottles/data/bottles/bottles/A
# Then recreate the bottle in Bottles application
```

## 🎯 **Step-by-Step Troubleshooting**

### **Step 1: Try the Quick Fixes**
Start with Fix 1 (Clear Cache), then Fix 2 (Web Browser Login)

### **Step 2: Check Battle.net Version**
- Make sure you're using the latest Battle.net launcher
- Older versions have known authentication issues

### **Step 3: Check Internet Connection**
```bash
# Test if you can reach Battle.net servers
ping -c 4 us.battle.net
```

### **Step 4: Check Wine Configuration**
```bash
# Open Wine configuration
WINEPREFIX=~/.local/share/bottles/bottles/HDT winecfg
```
- Set Windows version to **Windows 10**
- In Graphics tab, disable **"Allow the window manager to decorate windows"**

### **Step 5: Run Battle.net with Debug Info**
```bash
WINEPREFIX=~/.local/share/bottles/bottles/HDT WINEDEBUG=+wininet,+winhttp wine "drive_c/Program Files (x86)/Battle.net/Battle.net Launcher.exe"
```

## 🔍 **Common Error Messages and Solutions**

### **"BLZBNTBGS000003F8" Error**
- **Solution**: Clear cache (Fix 1) and restart Battle.net

### **"We're having trouble logging you in"**
- **Solution**: Use web browser login (Fix 2)

### **Password field clears/disappears**
- **Solution**: This is the exact issue you're having - use Fix 2 (Web Browser Login)

### **"Unable to connect to the authentication service"**
- **Solution**: Check internet connection and try offline mode trick (Fix 3)

### **Stuck on "Logging in..." forever**
- **Solution**: Kill Battle.net process and try web browser login

## 🚀 **What We've Already Applied**

✅ **Registry fixes applied** - Windows 10 compatibility, TLS 1.2/1.3 enabled
✅ **Authentication improvements** - IE settings optimized
✅ **Hardware acceleration disabled** - Prevents rendering issues
✅ **Security protocols updated** - Better HTTPS connectivity

## 🎮 **Launch Battle.net Now**

1. **Close Battle.net completely** if it's running
2. **Launch from Bottles** 
3. **Try web browser login** when the login screen appears
4. **If that doesn't work**, try clearing cache first

## 📞 **Still Having Issues?**

If none of these fixes work:

1. **Check Bottles logs**: Look for error messages in Bottles console
2. **Try different Wine version**: Switch to Wine 8.0 or 9.0 in Bottles
3. **Reinstall Battle.net**: Download fresh installer from Blizzard
4. **Use Lutris instead**: Some users have better luck with Lutris than Bottles

## 🎯 **Success Indicators**

When Battle.net login is working:
- ✅ Login screen accepts your credentials
- ✅ No password field clearing
- ✅ Successful authentication to Blizzard servers
- ✅ Game library loads properly
- ✅ Can launch Hearthstone without issues
