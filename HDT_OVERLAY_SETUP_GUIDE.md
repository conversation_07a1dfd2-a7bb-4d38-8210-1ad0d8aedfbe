# HDT Overlay Fix Guide for Hyprland/Wayland + NVIDIA

This guide will help you fix Hearthstone Deck Tracker (HDT) overlay issues on your Hyprland/Wayland setup with NVIDIA GPU.

## 🎯 What This Fixes

- ✅ Black overlay background → Transparent overlay
- ✅ Flickering overlay → Stable overlay
- ✅ Overlay not showing → Always visible overlay
- ✅ Overlay behind game → Overlay on top

## 🚀 Quick Setup (Automated)

1. **Run the automated fix script:**
   ```bash
   cd ~/.config/hypr
   ./scripts/hdt-overlay-fix.sh
   ```

2. **Restart Hyprland:**
   ```bash
   hyprctl reload
   ```

3. **Follow the on-screen instructions**

## 🔧 Manual Setup (Step by Step)

### Step 1: Hyprland Configuration

The window rules have been automatically added to `~/.config/hypr/conf/window_rules.conf`:

- HDT overlay windows are set to float and stay pinned on top
- Animations are disabled to prevent flickering
- Opacity is set to 95% for better visibility
- Click-through behavior is configured

### Step 2: Environment Variables

Enhanced NVIDIA/Wayland settings have been added to `~/.config/hypr/conf/env_var.conf`:

- WPF hardware acceleration disabled (fixes black background)
- Wine DLL overrides for better compatibility
- NVIDIA-specific optimizations

### Step 3: Wine/Bottles Configuration

**Option A: Automatic (Recommended)**
```bash
# The script will find your Bottles prefix automatically
./scripts/hdt-overlay-fix.sh
```

**Option B: Manual**
```bash
# Find your Hearthstone bottle
ls ~/.local/share/bottles/bottles/

# Apply registry fixes (replace with your actual path)
cd ~/.config/hypr
wine regedit scripts/wine-hdt-setup.reg
```

### Step 4: Hearthstone Game Settings

**CRITICAL: Change to Windowed Mode**

1. Launch Hearthstone
2. Go to **Options → Graphics**
3. Change **Display Mode** from **Fullscreen** to **Windowed**
4. Set resolution to your monitor's native resolution
5. Apply settings and restart Hearthstone

**Why this is necessary:**
- Fullscreen mode on Wayland blocks overlays
- Windowed mode allows proper overlay rendering
- No performance impact on modern systems

### Step 5: HDT Application Settings

**In Hearthstone Deck Tracker:**

1. **Options → Overlay:**
   - ✅ Enable "Player Deck"
   - ✅ Enable "Opponent Deck" 
   - Set opacity to 95%
   - ✅ Enable "Always on top"
   - ❌ Disable "Hide in background"
   - ❌ Disable "Hide in menu"

2. **Options → General:**
   - ❌ Disable hardware acceleration (if available)
   - ✅ Enable "Start with Windows" (optional)

## 🧪 Testing the Fix

1. **Start both applications:**
   ```bash
   # Launch HDT first
   # Then launch Hearthstone (in windowed mode)
   ```

2. **Check overlay visibility:**
   - Overlay should appear over the game
   - Background should be transparent (not black)
   - No flickering should occur

3. **Test window behavior:**
   ```bash
   # Check if windows are detected properly
   hyprctl clients | grep -i "hearthstone\|hdt"
   ```

## 🔍 Troubleshooting

### Problem: Overlay still shows black background
**Solution:**
```bash
# Ensure WPF hardware acceleration is disabled
export DOTNET_SYSTEM_WINDOWS_MEDIA_DISABLEHARDWAREACCELERATION=1
export DOTNET_SYSTEM_WINDOWS_FORMS_DISABLEHARDWAREACCELERATION=1
```

### Problem: Overlay flickers at high refresh rate
**Solution:**
```bash
# Add to your monitor config (already included)
env = __GL_VRR_ALLOWED,0
env = __GL_SYNC_TO_VBLANK,1
```

### Problem: Overlay not staying on top
**Solution:**
- Ensure Hearthstone is in windowed mode (not fullscreen)
- Check that window rules are applied: `hyprctl clients`

### Problem: Can't find Bottles prefix
**Solution:**
```bash
# Find manually
find ~/.local/share/bottles -name "*.yml" -exec grep -l -i hearthstone {} \;

# Or specify manually
./scripts/hdt-overlay-fix.sh /path/to/your/bottles/prefix
```

## 📋 Configuration Files Modified

- `~/.config/hypr/conf/window_rules.conf` - HDT window rules
- `~/.config/hypr/conf/env_var.conf` - Environment variables
- `~/.config/hdt/config.xml` - HDT configuration
- Wine registry (via Bottles) - Compatibility fixes

## 🎮 Performance Notes

- **Windowed mode impact:** Negligible on RTX 4090
- **Overlay rendering:** Uses minimal GPU resources
- **Memory usage:** HDT typically uses 50-100MB RAM

## 🔄 Reverting Changes

If you need to revert the changes:

1. **Remove HDT window rules:**
   ```bash
   # Edit ~/.config/hypr/conf/window_rules.conf
   # Remove the HDT section (lines 112-179)
   ```

2. **Remove environment variables:**
   ```bash
   # Edit ~/.config/hypr/conf/env_var.conf  
   # Remove the HDT section (lines 84-101)
   ```

3. **Reload Hyprland:**
   ```bash
   hyprctl reload
   ```

## 📞 Support

If you encounter issues:

1. Check Hyprland logs: `journalctl -f -u hyprland`
2. Check Wine logs: `WINEDEBUG=+all wine ...`
3. Verify window rules: `hyprctl clients`
4. Test with minimal setup (HDT + Hearthstone only)

## 🎉 Success Indicators

When everything is working correctly:
- ✅ HDT overlay appears transparently over Hearthstone
- ✅ No black background on overlay windows
- ✅ No flickering or instability
- ✅ Overlay stays visible during gameplay
- ✅ Game performance remains smooth
